import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { useIntl } from 'react-intl';
import { HelpContentSection } from '@/types/public/help-center';

/**
 * MarkdownRender Component
 *
 * A truly generic and reusable component that renders markdown content with multiple capabilities:
 * - Variable substitution for policy pages
 * - Heading ID mapping for blog table of contents
 * - Structured content sections for help articles
 * - Proper styling and accessibility
 *
 * @param content - The markdown content to render OR structured content sections
 * @param variables - An object containing key-value pairs for variable substitution
 * @param title - Optional title to display above the content
 * @param lastUpdated - Optional last updated date to display
 * @param headingIdMap - Optional mapping of heading text to IDs for table of contents
 * @param structuredContent - Optional structured content sections (for help articles)
 * @param locale - Current locale for structured content rendering
 */
interface MarkdownRenderProps {
  content?: string;
  variables?: Record<string, string>;
  title?: string;
  lastUpdated?: string;
  className?: string;
  headingIdMap?: { [key: string]: string };
  structuredContent?: HelpContentSection[];
  locale?: string;
}

const MarkdownRender: React.FC<MarkdownRenderProps> = ({
  content = '',
  variables = {},
  title,
  lastUpdated,
  className = '',
  headingIdMap = {},
  structuredContent,
  locale = 'en',
}) => {
  const intl = useIntl();

  // Helper function to get localized content for structured content
  const getLocalizedContent = (item: any) => {
    if (typeof item === 'string') return item;
    return item[locale as keyof typeof item] || item.en || item;
  };

  // Process content by replacing all variables
  const processedContent = useMemo(() => {
    let result = content;

    // Replace all variables in the content
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{${key}}`, 'g');
      result = result.replace(regex, value);
    });

    // Process any double asterisk (**) that aren't being rendered correctly
    // This ensures that **bold text** is properly converted to <strong>bold text</strong>
    // First, ensure that we don't touch code blocks or inline code
    const codeBlockRegex = /```[\s\S]*?```/g;
    const inlineCodeRegex = /`[^`]+`/g;

    const codeBlocks: string[] = [];
    const inlineCodes: string[] = [];

    // Extract code blocks
    result = result.replace(codeBlockRegex, match => {
      codeBlocks.push(match);
      return `%%CODEBLOCK${codeBlocks.length - 1}%%`;
    });

    // Extract inline code
    result = result.replace(inlineCodeRegex, match => {
      inlineCodes.push(match);
      return `%%INLINECODE${inlineCodes.length - 1}%%`;
    });

    // Now process the ** for bold text
    result = result.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Restore code blocks
    codeBlocks.forEach((block, i) => {
      result = result.replace(`%%CODEBLOCK${i}%%`, block);
    });

    // Restore inline code
    inlineCodes.forEach((code, i) => {
      result = result.replace(`%%INLINECODE${i}%%`, code);
    });

    return result;
  }, [content, variables]);

  // Render structured content sections (for help articles)
  const renderStructuredContent = () => {
    if (!structuredContent) return null;

    const renderContentItem = (item: HelpContentSection, index: number) => {
      switch (item.type) {
        case 'heading':
          return (
            <h2 key={index} className="mb-4 mt-8 text-2xl font-bold first:mt-0">
              {getLocalizedContent(item.content)}
            </h2>
          );

        case 'paragraph':
          return (
            <p key={index} className="mb-4 leading-relaxed">
              {getLocalizedContent(item.content)}
            </p>
          );

        case 'list':
          return (
            <div key={index} className="mb-6">
              <p className="mb-3 font-medium">
                {getLocalizedContent(item.content)}
              </p>
              <ul className="ml-4 list-inside list-disc space-y-2">
                {item.items?.map((listItem: any, listIndex: number) => (
                  <li key={listIndex} className="leading-relaxed">
                    {getLocalizedContent(listItem)}
                  </li>
                ))}
              </ul>
            </div>
          );

        case 'code':
          return (
            <div key={index} className="mb-6">
              <pre className="overflow-x-auto rounded-lg bg-muted p-4">
                <code className="font-mono text-sm">
                  {getLocalizedContent(item.content)}
                </code>
              </pre>
            </div>
          );

        case 'note':
          return (
            <div
              key={index}
              className="mb-6 rounded-r-lg border-l-4 border-blue-400 bg-blue-50 p-4 dark:border-blue-400 dark:bg-blue-950/20"
            >
              <div className="flex items-start">
                <svg
                  className="mr-3 mt-0.5 h-5 w-5 flex-shrink-0 text-blue-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="leading-relaxed text-blue-800 dark:text-blue-200">
                  {getLocalizedContent(item.content)}
                </p>
              </div>
            </div>
          );

        case 'warning':
          return (
            <div
              key={index}
              className="mb-6 rounded-r-lg border-l-4 border-yellow-400 bg-yellow-50 p-4 dark:border-yellow-400 dark:bg-yellow-950/20"
            >
              <div className="flex items-start">
                <svg
                  className="mr-3 mt-0.5 h-5 w-5 flex-shrink-0 text-yellow-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <p className="leading-relaxed text-yellow-800 dark:text-yellow-200">
                  {getLocalizedContent(item.content)}
                </p>
              </div>
            </div>
          );

        default:
          return null;
      }
    };

    return (
      <div className="help-content">
        {structuredContent.map((item, index) => renderContentItem(item, index))}
      </div>
    );
  };

  // Custom components for rendering markdown with proper styling
  const components = {
    // Headings with proper spacing, sizing, and optional ID mapping for table of contents
    h1: ({ children, ...props }: any) => {
      const text = React.Children.toArray(children).join('');
      const id =
        headingIdMap[text] ||
        text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

      return (
        <h1
          id={id}
          className="mb-4 mt-8 scroll-mt-8 text-3xl font-bold"
          {...props}
        >
          {children}
        </h1>
      );
    },
    h2: ({ children, ...props }: any) => {
      const text = React.Children.toArray(children).join('');
      const id =
        headingIdMap[text] ||
        text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

      return (
        <h2
          id={id}
          className="mb-4 mt-8 scroll-mt-8 border-b border-border pb-2 text-2xl font-semibold text-foreground"
          {...props}
        >
          {children}
        </h2>
      );
    },
    h3: ({ children, ...props }: any) => {
      const text = React.Children.toArray(children).join('');
      const id =
        headingIdMap[text] ||
        text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

      return (
        <h3
          id={id}
          className="mb-3 mt-6 scroll-mt-8 text-xl font-semibold text-foreground"
          {...props}
        >
          {children}
        </h3>
      );
    },
    h4: ({ children, ...props }: any) => {
      const text = React.Children.toArray(children).join('');
      const id =
        headingIdMap[text] ||
        text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');

      return (
        <h4
          id={id}
          className="mb-2 mt-4 scroll-mt-8 text-lg font-semibold text-foreground"
          {...props}
        >
          {children}
        </h4>
      );
    },

    // Paragraphs with proper spacing
    p: ({ ...props }: any) => (
      <p className="my-4 leading-relaxed text-foreground" {...props} />
    ),

    // Lists with proper indentation and bullets
    ul: ({ ...props }: any) => (
      <ul className="my-4 list-disc space-y-2 pl-8" {...props} />
    ),
    ol: ({ ...props }: any) => (
      <ol className="my-4 list-decimal space-y-2 pl-8" {...props} />
    ),
    li: ({ ...props }: any) => <li className="mb-1" {...props} />,

    // Links with proper styling
    a: ({ href, ...props }: any) => {
      const isMailTo = href?.startsWith('mailto:');
      if (isMailTo) {
        return (
          <a href={href} className="text-primary hover:underline" {...props} />
        );
      }

      const isExternal = href?.startsWith('http');
      if (isExternal) {
        return (
          <a
            href={href}
            className="text-primary hover:underline"
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        );
      }

      return (
        <a
          href={href || '#'}
          className="text-primary hover:underline"
          {...props}
        />
      );
    },

    // Block quotes with proper styling
    blockquote: ({ ...props }: any) => (
      <blockquote
        className="my-4 border-l-4 border-border pl-4 italic text-muted-foreground"
        {...props}
      />
    ),

    // Code blocks with proper styling
    code: ({ inline, className, children, ...props }: any) => {
      if (inline) {
        return (
          <code
            className="rounded bg-muted px-1 py-0.5 font-mono text-sm text-muted-foreground"
            {...props}
          >
            {children}
          </code>
        );
      }

      return (
        <pre className="my-4 overflow-x-auto rounded-md bg-muted p-4">
          <code className={`${className || ''} font-mono text-sm`} {...props}>
            {children}
          </code>
        </pre>
      );
    },

    // Tables with proper styling
    table: ({ ...props }: any) => (
      <div className="my-6 overflow-x-auto">
        <table className="min-w-full border border-border bg-card" {...props} />
      </div>
    ),
    thead: ({ ...props }: any) => <thead className="bg-muted" {...props} />,
    tbody: ({ ...props }: any) => (
      <tbody className="divide-y divide-border" {...props} />
    ),
    tr: ({ ...props }: any) => <tr className="hover:bg-accent" {...props} />,
    th: ({ ...props }: any) => (
      <th
        className="border-b px-4 py-3 text-left text-sm font-semibold text-card-foreground"
        {...props}
      />
    ),
    td: ({ ...props }: any) => (
      <td
        className="border-b px-4 py-3 text-sm text-card-foreground"
        {...props}
      />
    ),

    // Strong and emphasis with proper styling
    strong: ({ ...props }: any) => (
      <strong className="font-semibold" {...props} />
    ),
    em: ({ ...props }: any) => <em className="italic" {...props} />,

    // Horizontal rule with proper styling
    hr: () => <hr className="my-8" />,
  };

  return (
    <div className={`container mx-auto px-4 py-8 ${className}`}>
      <div className="mx-auto max-w-4xl">
        {title && <h1 className="mb-6 text-3xl font-bold">{title}</h1>}

        {lastUpdated && (
          <p className="mb-6 text-sm text-muted-foreground">
            {intl.formatMessage(
              { id: 'public.policy.last.updated' },
              { date: lastUpdated }
            )}
          </p>
        )}

        {/* Render structured content if provided, otherwise render markdown */}
        {structuredContent ? (
          renderStructuredContent()
        ) : (
          <div className="prose prose-lg dark:prose-invert policy-content max-w-none">
            <ReactMarkdown
              components={components}
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {processedContent}
            </ReactMarkdown>
          </div>
        )}
      </div>
    </div>
  );
};

export default MarkdownRender;
