import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Label } from '@/components/ui/shadcn/label';
import { Textarea } from '@/components/ui/shadcn/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/shadcn/select';
import {
  Alert,
  AlertTitle,
  AlertDescription,
} from '@/components/ui/shadcn/alert';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { SupportTicketFormType } from '@/types/public/support';
import '@/styles/animations.css';

interface SupportTicketFormProps {
  onSubmit: (_formData: SupportTicketFormType) => Promise<void>;
  onCancel?: () => void;
  isSubmitting?: boolean;
}

export function SupportTicketForm({
  onSubmit,
  onCancel,
  isSubmitting = false,
}: SupportTicketFormProps) {
  const intl = useIntl();
  const [error, setError] = useState('');

  const [formData, setFormData] = useState<SupportTicketFormType>({
    contactInfo: {
      name: '',
      email: '',
      company: '',
    },
    ticketInfo: {
      subject: '',
      category: '',
      priority: 'medium',
      description: '',
    },
    attachments: [],
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Contact info validation
    if (!formData.contactInfo.name.trim()) {
      errors.name = intl.formatMessage({ id: 'form.validation.required' });
    }
    if (!formData.contactInfo.email.trim()) {
      errors.email = intl.formatMessage({ id: 'form.validation.required' });
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactInfo.email)) {
      errors.email = intl.formatMessage({
        id: 'form.validation.email.invalid',
      });
    }

    // Ticket info validation
    if (!formData.ticketInfo.subject.trim()) {
      errors.subject = intl.formatMessage({ id: 'form.validation.required' });
    }
    if (!formData.ticketInfo.category) {
      errors.category = intl.formatMessage({ id: 'form.validation.required' });
    }
    if (!formData.ticketInfo.description.trim()) {
      errors.description = intl.formatMessage({
        id: 'form.validation.required',
      });
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    const keys = field.split('.');
    setFormData(prev => {
      const newData = { ...prev };
      let current: any = newData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newData;
    });

    // Clear validation error when user starts typing
    if (validationErrors[field.split('.').pop() || field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field.split('.').pop() || field];
        return newErrors;
      });
    }
  };

  const handleFileChange = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        attachments: fileArray,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch {
      setError(intl.formatMessage({ id: 'form.error.submission' }));
    }
  };

  const categories = [
    {
      value: 'technical',
      label: intl.formatMessage({ id: 'public.support.category.technical' }),
    },
    {
      value: 'billing',
      label: intl.formatMessage({ id: 'public.support.category.billing' }),
    },
    {
      value: 'account',
      label: intl.formatMessage({ id: 'public.support.category.account' }),
    },
    {
      value: 'feature',
      label: intl.formatMessage({ id: 'public.support.category.feature' }),
    },
    {
      value: 'other',
      label: intl.formatMessage({ id: 'public.support.category.other' }),
    },
  ];

  const priorities = [
    {
      value: 'low',
      label: intl.formatMessage({ id: 'public.support.priority.low' }),
    },
    {
      value: 'medium',
      label: intl.formatMessage({ id: 'public.support.priority.medium' }),
    },
    {
      value: 'high',
      label: intl.formatMessage({ id: 'public.support.priority.high' }),
    },
    {
      value: 'urgent',
      label: intl.formatMessage({ id: 'public.support.priority.urgent' }),
    },
  ];

  return (
    <form
      onSubmit={handleSubmit}
      className="animate-fadeIn mx-auto max-w-4xl space-y-8"
    >
      {error && (
        <Alert variant="destructive" className="animate-fadeIn">
          <AlertTitle>{intl.formatMessage({ id: 'common.error' })}</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Contact Information */}
      <Card className="animate-fadeIn">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'public.support.ticket.contact.title' })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="name">
                {intl.formatMessage({ id: 'public.support.ticket.name' })} *
              </Label>
              <Input
                id="name"
                value={formData.contactInfo.name}
                onChange={e =>
                  handleInputChange('contactInfo.name', e.target.value)
                }
                className={validationErrors.name ? 'border-red-500' : ''}
              />
              {validationErrors.name && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.name}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="email">
                {intl.formatMessage({ id: 'public.support.ticket.email' })} *
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.contactInfo.email}
                onChange={e =>
                  handleInputChange('contactInfo.email', e.target.value)
                }
                className={validationErrors.email ? 'border-red-500' : ''}
              />
              {validationErrors.email && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.email}
                </p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="company">
              {intl.formatMessage({ id: 'public.support.ticket.company' })}
            </Label>
            <Input
              id="company"
              value={formData.contactInfo.company}
              onChange={e =>
                handleInputChange('contactInfo.company', e.target.value)
              }
              placeholder={intl.formatMessage({
                id: 'public.support.ticket.company.placeholder',
              })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Ticket Information */}
      <Card className="animate-fadeIn animation-delay-200">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'public.support.ticket.details.title' })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="subject">
              {intl.formatMessage({ id: 'public.support.ticket.subject' })} *
            </Label>
            <Input
              id="subject"
              value={formData.ticketInfo.subject}
              onChange={e =>
                handleInputChange('ticketInfo.subject', e.target.value)
              }
              placeholder={intl.formatMessage({
                id: 'public.support.ticket.subject.placeholder',
              })}
              className={validationErrors.subject ? 'border-red-500' : ''}
            />
            {validationErrors.subject && (
              <p className="mt-1 text-sm text-red-500">
                {validationErrors.subject}
              </p>
            )}
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="category">
                {intl.formatMessage({ id: 'public.support.ticket.category' })} *
              </Label>
              <Select
                value={formData.ticketInfo.category}
                onValueChange={value =>
                  handleInputChange('ticketInfo.category', value)
                }
              >
                <SelectTrigger
                  className={validationErrors.category ? 'border-red-500' : ''}
                >
                  <SelectValue
                    placeholder={intl.formatMessage({
                      id: 'public.support.ticket.category.placeholder',
                    })}
                  />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.category && (
                <p className="mt-1 text-sm text-red-500">
                  {validationErrors.category}
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="priority">
                {intl.formatMessage({ id: 'public.support.ticket.priority' })}
              </Label>
              <Select
                value={formData.ticketInfo.priority}
                onValueChange={value =>
                  handleInputChange('ticketInfo.priority', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {priorities.map(priority => (
                    <SelectItem key={priority.value} value={priority.value}>
                      {priority.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">
              {intl.formatMessage({ id: 'public.support.ticket.description' })} *
            </Label>
            <Textarea
              id="description"
              rows={6}
              value={formData.ticketInfo.description}
              onChange={e =>
                handleInputChange('ticketInfo.description', e.target.value)
              }
              placeholder={intl.formatMessage({
                id: 'public.support.ticket.description.placeholder',
              })}
              className={validationErrors.description ? 'border-red-500' : ''}
            />
            {validationErrors.description && (
              <p className="mt-1 text-sm text-red-500">
                {validationErrors.description}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Attachments */}
      <Card className="animate-fadeIn animation-delay-300">
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({
              id: 'public.support.ticket.attachments.title',
            })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="attachments">
              {intl.formatMessage({ id: 'public.support.ticket.attachments' })}
            </Label>
            <Input
              id="attachments"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.gif"
              onChange={e => handleFileChange(e.target.files)}
            />
            <p className="mt-1 text-sm text-muted-foreground">
              {intl.formatMessage({
                id: 'public.support.ticket.attachments.help',
              })}
            </p>
            {formData.attachments.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium">
                  {intl.formatMessage(
                    { id: 'public.support.ticket.attachments.selected' },
                    { count: formData.attachments.length }
                  )}
                </p>
                <ul className="mt-1 text-sm text-muted-foreground">
                  {formData.attachments.map((file, index) => (
                    <li key={index}>
                      {file.name} ({Math.round(file.size / 1024)} KB)
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="animate-fadeIn animation-delay-400 flex justify-end gap-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            {intl.formatMessage({ id: 'common.cancel' })}
          </Button>
        )}
        <Button
          type="submit"
          disabled={isSubmitting}
          className="shimmer-effect"
        >
          {isSubmitting
            ? intl.formatMessage({ id: 'common.submitting' })
            : intl.formatMessage({ id: 'public.support.ticket.submit' })}
        </Button>
      </div>
    </form>
  );
}
