import * as React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { memo, useMemo, useCallback } from 'react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/shadcn/breadcrumb';

/**
 * Interface for breadcrumb item properties
 * @property {string} title - The display title of the breadcrumb (fallback)
 * @property {string} translationKey - The i18n key for the breadcrumb title translation
 * @property {string} href - The URL the breadcrumb links to
 */
interface BreadcrumbItemData {
  title: string;
  translationKey?: string;
  href?: string;
}

/**
 * Props for the NavBreadcrumb component
 * @property {BreadcrumbItemData[]} items - Optional custom breadcrumb items
 */
interface BreadcrumbNavProps {
  items?: BreadcrumbItemData[];
}

/**
 * Navigation breadcrumb component that supports internationalization
 * Displays a hierarchical navigation path based on the current URL or provided items
 */
export const NavBreadcrumb = memo(({ items }: BreadcrumbNavProps) => {
  const location = useLocation();
  const intl = useIntl();

  // Memoize breadcrumb items generation with i18n support
  const breadcrumbItems = useMemo(
    () => items || generateBreadcrumbItems(location.pathname, intl),
    [items, location.pathname, intl]
  );

  // Memoize the translation function
  const getTranslatedTitle = useCallback(
    (item: BreadcrumbItemData) => {
      // Priority 1: Use provided specific translation key
      if (item.translationKey && intl.messages[item.translationKey]) {
        return intl.formatMessage({ id: item.translationKey });
      }

      // Priority 2: Try page namespace (for page titles)
      const pageKey = `public.${item.title.toLowerCase().replace(/\s+/g, '.')}`;
      if (intl.messages[pageKey]) {
        return intl.formatMessage({ id: pageKey });
      }

      // Priority 3: Try navigation namespace
      const navKey = `navigation.${item.title.toLowerCase().replace(/\s+/g, '.')}`;
      if (intl.messages[navKey]) {
        return intl.formatMessage({ id: navKey });
      }

      // Priority 4: Try nav.main namespace
      const navMainKey = `nav.main.${item.title.toLowerCase().replace(/\s+/g, '.')}`;
      if (intl.messages[navMainKey]) {
        return intl.formatMessage({ id: navMainKey });
      }

      // Priority 5: Check common translations
      if (intl.messages[item.title.toLowerCase().replace(/\s+/g, '.')]) {
        return intl.formatMessage({
          id: item.title.toLowerCase().replace(/\s+/g, '.'),
        });
      }

      // Fallback: Use the title as is
      return item.title;
    },
    [intl]
  );

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {/* Add Home as first item */}
        <BreadcrumbItem className="hidden md:block">
          <Link to="/" className="breadcrumb-link">
            {intl.formatMessage({ id: 'home' })}
          </Link>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />

        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const title = getTranslatedTitle(item);

          return (
            <React.Fragment key={`breadcrumb-group-${index}`}>
              <BreadcrumbItem>
                {isLast ? (
                  <BreadcrumbPage>{title}</BreadcrumbPage>
                ) : (
                  <Link to={item.href || '#'} className="breadcrumb-link">
                    {title}
                  </Link>
                )}
              </BreadcrumbItem>
              {!isLast && <BreadcrumbSeparator className="hidden md:block" />}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
});

NavBreadcrumb.displayName = 'NavBreadcrumb';

/**
 * Generates breadcrumb items from URL path with i18n support
 * @param {string} path - The current URL path
 * @param {IntlShape} intl - The intl object for translations
 * @returns {BreadcrumbItemData[]} - Array of breadcrumb items
 */
function generateBreadcrumbItems(
  path: string,
  intl: any
): BreadcrumbItemData[] {
  const paths = path.split('/').filter(Boolean);

  if (paths.length === 0) {
    return [];
  }

  return paths.map((segment, index) => {
    const formattedTitle = formatBreadcrumbTitle(segment);
    const href = `/${paths.slice(0, index + 1).join('/')}`;

    // Try to find appropriate translation keys for this path segment
    const segmentLower = segment.toLowerCase().replace(/-/g, '.');

    // Check various possible translation namespaces
    const possibleTranslationKeys = [
      `public.${segmentLower}`,
      `navigation.${segmentLower}`,
      `nav.main.${segmentLower}`,
      segmentLower,
    ];

    // Find the first translation key that exists
    const translationKey = possibleTranslationKeys.find(
      key => !!intl.messages[key]
    );

    return {
      title: formattedTitle,
      translationKey: translationKey,
      href,
    };
  });
}

/**
 * Formats a URL segment into a readable breadcrumb title
 * Used as fallback when no translation is available
 * @param {string} segment - URL path segment
 * @returns {string} - Formatted title
 */
function formatBreadcrumbTitle(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
