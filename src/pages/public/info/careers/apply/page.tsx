import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { jobOpenings, getTranslatedText } from '@/data/public/careers';
import { JobApplicationForm } from '@/components/forms/job-application-form';
import { JobApplicationForm as JobApplicationFormType } from '@/types/public/careers';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { ArrowLeft } from 'lucide-react';
import '@/styles/animations.css';

/**
 * Job Application Page
 *
 * Displays job details and provides application form for specific job positions.
 * Handles job application submission and confirmation.
 *
 * @returns {JSX.Element} The Job Application Page component
 */
export default function Page() {
  const intl = useIntl();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.careers; // Will be updated with job-specific metadata
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;

  // Find the job by ID
  const job = jobOpenings.find(job => job.id.toString() === id);

  // Update document metadata
  useEffect(() => {
    if (job) {
      const jobTitle = getTranslatedText(job.title, locale);
      const customMetadata = {
        ...metadata,
        title: intl.formatMessage(
          { id: 'public.careers.application.title' },
          { job: jobTitle, company: displayName }
        ),
        description: intl.formatMessage(
          { id: 'public.careers.application.description' },
          { job: jobTitle }
        ),
      };
      updatePageMetadata(customMetadata);
    } else {
      updatePageMetadata(metadata);
    }
  }, [displayName, intl, job, job?.id, locale, metadata]); // Simplified dependencies to prevent unnecessary re-renders

  const handleApplicationSubmit = async (formData: JobApplicationFormType) => {
    // In a real application, this would submit to an API
    console.log('Job Application Submitted:', {
      jobId: id,
      ...formData,
    });

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Here you would typically:
    // 1. Upload files to cloud storage
    // 2. Send application data to backend API
    // 3. Send confirmation email to applicant
    // 4. Notify HR team
  };

  const handleBack = () => {
    navigate('/careers');
  };

  // If job not found, show error
  if (!job) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-2xl text-center">
          <h1 className="animate-fadeIn mb-4 text-3xl font-bold">
            {intl.formatMessage({
              id: 'public.careers.application.notFound.title',
            })}
          </h1>
          <p className="animate-fadeIn animation-delay-200 mb-6 text-muted-foreground">
            {intl.formatMessage({
              id: 'public.careers.application.notFound.message',
            })}
          </p>
          <Button
            onClick={handleBack}
            variant="outline"
            className="animate-fadeIn animation-delay-300"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {intl.formatMessage({
              id: 'public.careers.application.backToCareers',
            })}
          </Button>
        </div>
      </div>
    );
  }

  const jobTitle = getTranslatedText(job.title, locale);
  const jobLocation = getTranslatedText(job.location, locale);
  const jobType = getTranslatedText(job.type, locale);
  const jobDescription = getTranslatedText(job.description, locale);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-6xl">
        {/* Header with back button */}
        <div className="animate-fadeIn mb-8 flex items-center">
          <Button
            onClick={handleBack}
            variant="ghost"
            size="sm"
            className="mr-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {intl.formatMessage({
              id: 'public.careers.application.backToCareers',
            })}
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {intl.formatMessage(
                { id: 'public.careers.application.title' },
                { job: jobTitle }
              )}
            </h1>
            <p className="text-muted-foreground">
              {intl.formatMessage(
                { id: 'public.careers.application.subtitle' },
                { company: displayName }
              )}
            </p>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Job Details Sidebar */}
          <div className="lg:col-span-1">
            <Card className="animate-fadeIn animation-delay-200 sticky top-8">
              <CardHeader>
                <CardTitle>
                  {intl.formatMessage({
                    id: 'public.careers.application.jobDetails',
                  })}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold">{jobTitle}</h3>
                  <div className="mt-2 flex flex-wrap gap-2">
                    <span className="inline-block rounded bg-primary/10 px-2 py-1 text-sm text-primary">
                      {jobLocation}
                    </span>
                    <span className="inline-block rounded bg-green-100 px-2 py-1 text-sm text-green-800">
                      {jobType}
                    </span>
                  </div>
                </div>

                <div>
                  <h4 className="mb-2 font-medium">
                    {intl.formatMessage(
                      { id: 'public.careers.openings.description' },
                      { defaultMessage: 'Job Description' }
                    )}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {jobDescription}
                  </p>
                </div>

                <div>
                  <h4 className="mb-2 font-medium">
                    {intl.formatMessage({
                      id: 'public.careers.openings.responsibilities',
                    })}
                  </h4>
                  <ul className="space-y-1">
                    {job.responsibilities
                      .slice(0, 3)
                      .map((responsibility, index) => (
                        <li key={index} className="flex items-start text-sm">
                          <svg
                            className="mr-2 mt-0.5 h-3 w-3 flex-shrink-0 text-primary"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          <span>
                            {getTranslatedText(responsibility, locale)}
                          </span>
                        </li>
                      ))}
                  </ul>
                  {job.responsibilities.length > 3 && (
                    <p className="mt-2 text-xs text-muted-foreground">
                      {intl.formatMessage(
                        { id: 'public.careers.application.andMore' },
                        { count: job.responsibilities.length - 3 }
                      )}
                    </p>
                  )}
                </div>

                <div>
                  <h4 className="mb-2 font-medium">
                    {intl.formatMessage({
                      id: 'public.careers.openings.requirements',
                    })}
                  </h4>
                  <ul className="space-y-1">
                    {job.requirements.slice(0, 3).map((requirement, index) => (
                      <li key={index} className="flex items-start text-sm">
                        <svg
                          className="mr-2 mt-0.5 h-3 w-3 flex-shrink-0 text-primary"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span>{getTranslatedText(requirement, locale)}</span>
                      </li>
                    ))}
                  </ul>
                  {job.requirements.length > 3 && (
                    <p className="mt-2 text-xs text-muted-foreground">
                      {intl.formatMessage(
                        { id: 'public.careers.application.andMore' },
                        { count: job.requirements.length - 3 }
                      )}
                    </p>
                  )}
                </div>

                <div className="border-t pt-4">
                  <p className="text-xs text-muted-foreground">
                    {intl.formatMessage({
                      id: 'public.careers.application.equalOpportunity',
                    })}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Application Form */}
          <div className="lg:col-span-2">
            <JobApplicationForm
              jobId={id!}
              jobTitle={jobTitle}
              onSubmit={handleApplicationSubmit}
              onCancel={handleBack}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
