import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  ArrowLeft,
  Calendar,
  Clock,
  MapPin,
  Users,
  Share2,
  ExternalLink,
  Star,
  Tag,
  User,
  Globe,
  Twitter,
  Linkedin,
  CheckCircle,
  AlertCircle,
  ChevronRight,
} from 'lucide-react';
import { getEventBySlug, getRelatedEvents } from '@/data/public/events';
import { Event } from '@/types/public/events';
import MarkdownRender from '@/components/common/markdown-renderer';
import '@/styles/animations.css';

/**
 * Event Detail Page Component
 *
 * Displays detailed information about a specific event including:
 * - Full event content with description and schedule
 * - Speaker information and bios
 * - Registration details and pricing
 * - Location and venue information
 * - Related events
 *
 * @returns {JSX.Element} The Event Detail Page component
 */
export default function Page() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const intl = useIntl();
  const metadata = PAGE_METADATA.eventDetail;

  const [event, setEvent] = useState<Event | null>(null);
  const [relatedEvents, setRelatedEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    header: true,
    content: true,
    speakers: true,
    schedule: true,
    registration: true,
    related: true,
  });

  // Refs for the sections
  const headerRef = useRef(null);
  const contentRef = useRef(null);
  const speakersRef = useRef(null);
  const scheduleRef = useRef(null);
  const registrationRef = useRef(null);
  const relatedRef = useRef(null);

  useEffect(() => {
    if (!slug) {
      navigate('/events');
      return;
    }

    const eventData = getEventBySlug(slug);
    if (!eventData) {
      navigate('/events');
      return;
    }

    setEvent(eventData);
    setRelatedEvents(getRelatedEvents(eventData));
    setLoading(false);

    // Update document metadata
    const translatedMetadata = {
      ...metadata,
      title:
        eventData.title[intl.locale as keyof typeof eventData.title] ||
        eventData.title.en,
      description:
        eventData.excerpt[intl.locale as keyof typeof eventData.excerpt] ||
        eventData.excerpt.en,
    };
    updatePageMetadata(translatedMetadata);
  }, [slug, navigate, metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (headerRef.current) observer.observe(headerRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (speakersRef.current) observer.observe(speakersRef.current);
    if (scheduleRef.current) observer.observe(scheduleRef.current);
    if (registrationRef.current) observer.observe(registrationRef.current);
    if (relatedRef.current) observer.observe(relatedRef.current);

    return () => {
      observer.disconnect();
    };
  }, [event]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString(intl.locale, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(intl.locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.startDate);
    const endDate = new Date(event.endDate);

    if (now < startDate) return 'upcoming';
    if (now >= startDate && now <= endDate) return 'ongoing';
    return 'completed';
  };

  const handleShare = async () => {
    if (navigator.share && event) {
      try {
        await navigator.share({
          title: event.title[intl.locale as keyof typeof event.title],
          text: event.excerpt[intl.locale as keyof typeof event.excerpt],
          url: window.location.href,
        });
      } catch {
        // Fallback to copying URL to clipboard
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      // Fallback to copying URL to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!event) {
    return null;
  }

  const status = getEventStatus(event);

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <section
        ref={headerRef}
        data-section="header"
        className="px-4 py-16 sm:px-6 lg:px-8"
      >
        <div className="mx-auto max-w-4xl">
          <div
            className={`transition-all duration-1000 ${visibleSections.header ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            {/* Back Button */}
            <Link
              to="/events"
              className="mb-8 inline-flex items-center text-muted-foreground transition-colors hover:text-foreground"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {intl.formatMessage({
                id: 'caseStudies.backToEvents',
                defaultMessage: 'Back to Events',
              })}
            </Link>

            {/* Category and Status */}
            <div className="mb-4 flex items-center gap-3">
              <span
                className="rounded-full px-3 py-1 text-sm font-medium"
                style={{
                  backgroundColor: `${event.category.color}20`,
                  color: event.category.color,
                }}
              >
                {
                  event.category.name[
                    intl.locale as keyof typeof event.category.name
                  ]
                }
              </span>
              {event.featured && (
                <div className="flex items-center">
                  <Star className="mr-1 h-4 w-4 text-yellow-500" />
                  <span className="text-sm text-muted-foreground">
                    {intl.formatMessage({
                      id: 'public.events.featured',
                      defaultMessage: 'Featured',
                    })}
                  </span>
                </div>
              )}
              <span
                className={`rounded-full px-2 py-1 text-xs font-medium ${
                  status === 'upcoming'
                    ? 'bg-blue-100 text-blue-800'
                    : status === 'ongoing'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                }`}
              >
                {intl.formatMessage({
                  id: `events.status.${status}`,
                  defaultMessage:
                    status.charAt(0).toUpperCase() + status.slice(1),
                })}
              </span>
            </div>

            {/* Title */}
            <h1 className="mb-6 text-4xl font-bold text-foreground md:text-5xl lg:text-6xl">
              {event.title[intl.locale as keyof typeof event.title]}
            </h1>

            {/* Excerpt */}
            <p className="mb-8 text-xl leading-relaxed text-muted-foreground">
              {event.excerpt[intl.locale as keyof typeof event.excerpt]}
            </p>

            {/* Event Details */}
            <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-center">
                <Calendar className="mr-3 h-5 w-5 text-primary" />
                <div>
                  <div className="font-medium text-foreground">
                    {formatDate(event.startDate)}
                  </div>
                  {event.startDate !== event.endDate && (
                    <div className="text-sm text-muted-foreground">
                      to {formatDate(event.endDate)}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <Clock className="mr-3 h-5 w-5 text-primary" />
                <div>
                  <div className="font-medium text-foreground">
                    {formatTime(event.startDate)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {
                      new Date(event.startDate)
                        .toLocaleDateString(intl.locale, {
                          timeZoneName: 'short',
                        })
                        .split(', ')[1]
                    }
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <MapPin className="mr-3 h-5 w-5 text-primary" />
                <div>
                  <div className="font-medium text-foreground">
                    {event.location.name}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {event.location.city}, {event.location.country}
                  </div>
                </div>
              </div>

              {event.registration.maxAttendees && (
                <div className="flex items-center">
                  <Users className="mr-3 h-5 w-5 text-primary" />
                  <div>
                    <div className="font-medium text-foreground">
                      {event.registration.currentAttendees || 0} /{' '}
                      {event.registration.maxAttendees}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {intl.formatMessage({
                        id: 'public.events.attendees',
                        defaultMessage: 'attendees',
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4">
              {event.registration.isOpen &&
                event.registration.registrationUrl &&
                status === 'upcoming' && (
                  <a
                    href={event.registration.registrationUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center rounded-lg bg-primary px-6 py-3 font-medium text-primary-foreground transition-colors hover:bg-primary/90"
                  >
                    {intl.formatMessage({
                      id: 'public.events.register',
                      defaultMessage: 'Register Now',
                    })}
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </a>
                )}

              <button
                onClick={handleShare}
                className="inline-flex items-center rounded-lg border border-input px-6 py-3 font-medium transition-colors hover:bg-muted"
              >
                <Share2 className="mr-2 h-4 w-4" />
                {intl.formatMessage({
                  id: 'common.share',
                  defaultMessage: 'Share',
                })}
              </button>

              {event.socialLinks?.website && (
                <a
                  href={event.socialLinks.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center rounded-lg border border-input px-6 py-3 font-medium transition-colors hover:bg-muted"
                >
                  <Globe className="mr-2 h-4 w-4" />
                  {intl.formatMessage({
                    id: 'public.events.website',
                    defaultMessage: 'Event Website',
                  })}
                </a>
              )}
            </div>

            {/* Featured Image */}
            <div className="mt-8 overflow-hidden rounded-lg shadow-lg">
              <img
                src={event.featuredImage}
                alt={event.title[intl.locale as keyof typeof event.title]}
                className="h-64 w-full object-cover md:h-96"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section
        ref={contentRef}
        data-section="content"
        className="px-4 py-16 sm:px-6 lg:px-8"
      >
        <div className="mx-auto max-w-4xl">
          <div
            className={`transition-all duration-1000 ${visibleSections.content ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            <div className="prose prose-lg mb-12 max-w-none">
              <MarkdownRender
                content={
                  event.content[intl.locale as keyof typeof event.content]
                }
              />
            </div>

            {/* Requirements, Benefits, Target Audience */}
            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              {event.requirements && event.requirements.length > 0 && (
                <div className="rounded-lg bg-card p-6 shadow-lg">
                  <h3 className="mb-4 flex items-center text-xl font-bold text-foreground">
                    <AlertCircle className="mr-2 h-5 w-5 text-orange-500" />
                    {intl.formatMessage({
                      id: 'public.events.requirements',
                      defaultMessage: 'Requirements',
                    })}
                  </h3>
                  <ul className="space-y-2">
                    {event.requirements.map((requirement, index) => (
                      <li key={index} className="text-muted-foreground">
                        • {requirement[intl.locale as keyof typeof requirement]}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {event.benefits && event.benefits.length > 0 && (
                <div className="rounded-lg bg-card p-6 shadow-lg">
                  <h3 className="mb-4 flex items-center text-xl font-bold text-foreground">
                    <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
                    {intl.formatMessage({
                      id: 'public.events.benefits',
                      defaultMessage: "What You'll Get",
                    })}
                  </h3>
                  <ul className="space-y-2">
                    {event.benefits.map((benefit, index) => (
                      <li key={index} className="text-muted-foreground">
                        • {benefit[intl.locale as keyof typeof benefit]}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {event.targetAudience && event.targetAudience.length > 0 && (
                <div className="rounded-lg bg-card p-6 shadow-lg">
                  <h3 className="mb-4 flex items-center text-xl font-bold text-foreground">
                    <Users className="mr-2 h-5 w-5 text-blue-500" />
                    {intl.formatMessage({
                      id: 'public.events.targetAudience',
                      defaultMessage: 'Who Should Attend',
                    })}
                  </h3>
                  <ul className="space-y-2">
                    {event.targetAudience.map((audience, index) => (
                      <li key={index} className="text-muted-foreground">
                        • {audience[intl.locale as keyof typeof audience]}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Speakers Section */}
      {event.speakers.length > 0 && (
        <section
          ref={speakersRef}
          data-section="speakers"
          className="bg-muted/30 px-4 py-16 sm:px-6 lg:px-8"
        >
          <div className="mx-auto max-w-6xl">
            <div
              className={`transition-all duration-1000 ${visibleSections.speakers ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <h2 className="mb-12 text-center text-3xl font-bold text-foreground md:text-4xl">
                {intl.formatMessage({
                  id: 'public.events.speakers',
                  defaultMessage: 'Speakers',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                {event.speakers.map((speaker, index) => (
                  <div
                    key={speaker.id}
                    className={`stagger-card rounded-lg bg-card p-6 text-center shadow-lg ${
                      visibleSections.speakers
                        ? 'translate-y-0 opacity-100'
                        : 'translate-y-8 opacity-0'
                    }`}
                    style={{ transitionDelay: `${200 + index * 100}ms` }}
                  >
                    <img
                      src={
                        speaker.avatar ||
                        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
                      }
                      alt={speaker.name}
                      className="mx-auto mb-4 h-24 w-24 rounded-full"
                    />
                    <h3 className="mb-2 text-xl font-bold text-foreground">
                      {speaker.name}
                    </h3>
                    <p className="mb-1 font-medium text-primary">
                      {speaker.title[intl.locale as keyof typeof speaker.title]}
                    </p>
                    {speaker.company && (
                      <p className="mb-4 text-muted-foreground">
                        {speaker.company}
                      </p>
                    )}
                    <p className="mb-4 text-sm text-muted-foreground">
                      {speaker.bio[intl.locale as keyof typeof speaker.bio]}
                    </p>
                    {speaker.social && (
                      <div className="flex justify-center space-x-3">
                        {speaker.social.twitter && (
                          <a
                            href={speaker.social.twitter}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground transition-colors hover:text-primary"
                          >
                            <Twitter className="h-5 w-5" />
                          </a>
                        )}
                        {speaker.social.linkedin && (
                          <a
                            href={speaker.social.linkedin}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground transition-colors hover:text-primary"
                          >
                            <Linkedin className="h-5 w-5" />
                          </a>
                        )}
                        {speaker.social.website && (
                          <a
                            href={speaker.social.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-muted-foreground transition-colors hover:text-primary"
                          >
                            <Globe className="h-5 w-5" />
                          </a>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Schedule Section */}
      {event.schedule && event.schedule.length > 0 && (
        <section
          ref={scheduleRef}
          data-section="schedule"
          className="px-4 py-16 sm:px-6 lg:px-8"
        >
          <div className="mx-auto max-w-4xl">
            <div
              className={`transition-all duration-1000 ${visibleSections.schedule ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <h2 className="mb-12 text-center text-3xl font-bold text-foreground md:text-4xl">
                {intl.formatMessage({
                  id: 'public.events.schedule',
                  defaultMessage: 'Schedule',
                })}
              </h2>
              <div className="space-y-6">
                {event.schedule.map((item, index) => (
                  <div
                    key={item.id}
                    className={`stagger-card rounded-lg bg-card p-6 shadow-lg ${
                      visibleSections.schedule
                        ? 'translate-y-0 opacity-100'
                        : 'translate-y-8 opacity-0'
                    }`}
                    style={{ transitionDelay: `${100 + index * 50}ms` }}
                  >
                    <div className="flex flex-col gap-4 md:flex-row md:items-center">
                      <div className="flex-shrink-0 md:w-48">
                        <div className="text-sm text-muted-foreground">
                          {formatDateTime(item.startTime)} -{' '}
                          {formatTime(item.endTime)}
                        </div>
                        <div
                          className={`mt-1 inline-block rounded-full px-2 py-1 text-xs font-medium ${
                            item.type === 'keynote'
                              ? 'bg-purple-100 text-purple-800'
                              : item.type === 'workshop'
                                ? 'bg-green-100 text-green-800'
                                : item.type === 'break'
                                  ? 'bg-gray-100 text-gray-800'
                                  : item.type === 'networking'
                                    ? 'bg-blue-100 text-blue-800'
                                    : 'bg-orange-100 text-orange-800'
                          }`}
                        >
                          {item.type}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="mb-2 text-lg font-bold text-foreground">
                          {item.title[intl.locale as keyof typeof item.title]}
                        </h3>
                        <p className="mb-3 text-muted-foreground">
                          {
                            item.description[
                              intl.locale as keyof typeof item.description
                            ]
                          }
                        </p>
                        {item.speaker && (
                          <div className="flex items-center">
                            <User className="mr-2 h-4 w-4 text-muted-foreground" />
                            <span className="text-sm text-muted-foreground">
                              {item.speaker.name} -{' '}
                              {
                                item.speaker.title[
                                  intl.locale as keyof typeof item.speaker.title
                                ]
                              }
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Registration Section */}
      <section
        ref={registrationRef}
        data-section="registration"
        className="bg-muted/30 px-4 py-16 sm:px-6 lg:px-8"
      >
        <div className="mx-auto max-w-4xl">
          <div
            className={`transition-all duration-1000 ${visibleSections.registration ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            <div className="rounded-lg bg-card p-8 text-center shadow-lg">
              <h2 className="mb-6 text-3xl font-bold text-foreground">
                {status === 'upcoming'
                  ? intl.formatMessage({
                      id: 'public.events.registration.title',
                      defaultMessage: 'Event Registration',
                    })
                  : intl.formatMessage({
                      id: 'public.events.registration.completed',
                      defaultMessage: 'Event Completed',
                    })}
              </h2>

              {status === 'upcoming' && event.registration.isOpen ? (
                <>
                  <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div>
                      <div className="text-2xl font-bold text-primary">
                        {event.registration.price &&
                        event.registration.price.amount > 0
                          ? `$${event.registration.price.amount}`
                          : intl.formatMessage({
                              id: 'public.events.free',
                              defaultMessage: 'Free',
                            })}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'public.events.price',
                          defaultMessage: 'Price',
                        })}
                      </div>
                    </div>

                    {event.registration.maxAttendees && (
                      <div>
                        <div className="text-2xl font-bold text-foreground">
                          {event.registration.maxAttendees -
                            (event.registration.currentAttendees || 0)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {intl.formatMessage({
                            id: 'public.events.spotsLeft',
                            defaultMessage: 'Spots Left',
                          })}
                        </div>
                      </div>
                    )}

                    <div>
                      <div className="text-2xl font-bold text-foreground">
                        {formatDate(event.startDate)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {intl.formatMessage({
                          id: 'public.events.eventDate',
                          defaultMessage: 'Event Date',
                        })}
                      </div>
                    </div>
                  </div>

                  {event.registration.registrationUrl && (
                    <a
                      href={event.registration.registrationUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center rounded-lg bg-primary px-8 py-4 text-lg font-medium text-primary-foreground transition-colors hover:bg-primary/90"
                    >
                      {intl.formatMessage({
                        id: 'public.events.registerNow',
                        defaultMessage: 'Register Now',
                      })}
                      <ExternalLink className="ml-2 h-5 w-5" />
                    </a>
                  )}
                </>
              ) : status === 'upcoming' ? (
                <div className="text-muted-foreground">
                  {intl.formatMessage({
                    id: 'public.events.registrationClosed',
                    defaultMessage:
                      'Registration is currently closed for this event.',
                  })}
                </div>
              ) : (
                <div className="text-muted-foreground">
                  {intl.formatMessage({
                    id: 'public.events.eventCompleted',
                    defaultMessage:
                      'This event has been completed. Check out our upcoming events!',
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Related Events */}
      {relatedEvents.length > 0 && (
        <section
          ref={relatedRef}
          data-section="related"
          className="px-4 py-16 sm:px-6 lg:px-8"
        >
          <div className="mx-auto max-w-7xl">
            <div
              className={`transition-all duration-1000 ${visibleSections.related ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
            >
              <h2 className="mb-12 text-center text-3xl font-bold text-foreground md:text-4xl">
                {intl.formatMessage({
                  id: 'public.events.relatedEvents',
                  defaultMessage: 'Related Events',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                {relatedEvents.map((relatedEvent, index) => (
                  <article
                    key={relatedEvent.id}
                    className={`stagger-card overflow-hidden rounded-lg bg-card shadow-lg transition-all duration-300 hover:shadow-xl ${
                      visibleSections.related
                        ? 'translate-y-0 opacity-100'
                        : 'translate-y-8 opacity-0'
                    }`}
                    style={{ transitionDelay: `${100 + index * 100}ms` }}
                  >
                    <img
                      src={relatedEvent.featuredImage}
                      alt={
                        relatedEvent.title[
                          intl.locale as keyof typeof relatedEvent.title
                        ]
                      }
                      className="h-48 w-full object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-3 flex items-center justify-between">
                        <span
                          className="rounded-full px-2 py-1 text-xs font-medium"
                          style={{
                            backgroundColor: `${relatedEvent.category.color}20`,
                            color: relatedEvent.category.color,
                          }}
                        >
                          {
                            relatedEvent.category.name[
                              intl.locale as keyof typeof relatedEvent.category.name
                            ]
                          }
                        </span>
                      </div>
                      <h3 className="mb-3 text-lg font-bold text-foreground">
                        {
                          relatedEvent.title[
                            intl.locale as keyof typeof relatedEvent.title
                          ]
                        }
                      </h3>
                      <p className="mb-4 line-clamp-3 text-muted-foreground">
                        {
                          relatedEvent.excerpt[
                            intl.locale as keyof typeof relatedEvent.excerpt
                          ]
                        }
                      </p>
                      <div className="mb-4 flex items-center text-sm text-muted-foreground">
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>{formatDate(relatedEvent.startDate)}</span>
                      </div>
                      <Link
                        to={`/events/${relatedEvent.slug}`}
                        className="inline-flex items-center font-medium text-primary transition-colors hover:text-primary/80"
                      >
                        {intl.formatMessage({
                          id: 'caseStudies.learnMore',
                          defaultMessage: 'Learn More',
                        })}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Tags */}
      {event.tags.length > 0 && (
        <section className="border-t border-border px-4 py-8 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-4xl">
            <div className="flex flex-wrap items-center gap-3">
              <Tag className="h-5 w-5 text-muted-foreground" />
              {event.tags.map(tag => (
                <span
                  key={tag}
                  className="rounded-full bg-muted px-3 py-1 text-sm text-muted-foreground"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
