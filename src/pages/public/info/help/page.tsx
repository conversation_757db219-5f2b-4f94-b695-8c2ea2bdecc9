import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { Link } from 'react-router-dom';
import {
  HELP_CATEGORIES,
  POPULAR_GUIDES,
  RESOURCE_TYPES,
} from '@/data/public/help-center/';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Help Center Page Component
 *
 * Displays comprehensive help resources organized by categories.
 * Includes knowledge base articles, tutorials, FAQs, and guides.
 * Supports search functionality and quick navigation to specific help topics.
 *
 * Uses structured data from '@/data/help-center' for multilingual content.
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.help || PAGE_METADATA.faq; // Using FAQ metadata as fallback
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('getting-started');
  const currentLocale = intl.locale as 'en' | 'zh' | 'zh-TW';

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: false,
    quickLinks: false,
    categories: false,
    resources: false,
    contact: false,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const quickLinksRef = useRef(null);
  const categoriesRef = useRef(null);
  const resourcesRef = useRef(null);
  const contactRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'public.help.title' });
    const translatedDescription = intl.formatMessage({
      id: 'public.help.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (quickLinksRef.current) observer.observe(quickLinksRef.current);
    if (categoriesRef.current) observer.observe(categoriesRef.current);
    if (resourcesRef.current) observer.observe(resourcesRef.current);
    if (contactRef.current) observer.observe(contactRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Filter articles based on search query
  const filterBySearch = (item: any) => {
    if (!searchQuery) return true;
    const title = item.title?.[currentLocale] || item.title?.en || '';
    return title.toLowerCase().includes(searchQuery.toLowerCase());
  };

  // Handle category selection
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
  };

  // Get articles for the active category
  const getActiveArticles = () => {
    const category = HELP_CATEGORIES.find(cat => cat.id === activeCategory);
    if (!category) return [];
    return category.articles.filter(filterBySearch);
  };

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage(
                { id: 'public.help.center' },
                { defaultMessage: 'Help Center' }
              )}
            </h1>
            <p className="animate-fadeIn animation-delay-200 mb-8 text-xl opacity-90">
              {intl.formatMessage(
                { id: 'public.help.subtitle' },
                {
                  defaultMessage:
                    'Find answers, tutorials, and guides to help you get the most out of our platform',
                }
              )}
            </p>

            {/* Search bar */}
            <div className="animate-fadeIn animation-delay-300 relative mx-auto max-w-2xl">
              <div className="absolute left-3 top-3 text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <input
                type="text"
                placeholder={intl.formatMessage(
                  { id: 'public.help.search.placeholder' },
                  { defaultMessage: 'Search for help articles...' }
                )}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full rounded-lg border border-input bg-background py-3 pl-12 pr-4 text-foreground shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Quick links */}
      <div
        ref={quickLinksRef}
        data-section="quickLinks"
        className={`section-reveal border-b border-border bg-muted ${visibleSections.quickLinks ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-6">
          <div className="animate-fadeIn flex flex-wrap justify-center gap-4">
            <Link
              to="/faq"
              className="shimmer-effect flex items-center rounded-md border border-border bg-card px-4 py-2 text-card-foreground hover:bg-accent"
            >
              <svg
                className="mr-2 h-5 w-5 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              {intl.formatMessage(
                { id: 'public.faq' },
                { defaultMessage: 'Frequently Asked Questions' }
              )}
            </Link>
            <Link
              to="/support"
              className="shimmer-effect flex items-center rounded-md border border-border bg-card px-4 py-2 text-card-foreground hover:bg-accent"
            >
              <svg
                className="mr-2 h-5 w-5 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
                />
              </svg>
              {intl.formatMessage(
                { id: 'public.support' },
                { defaultMessage: 'Support' }
              )}
            </Link>
            <Link
              to="/contact"
              className="shimmer-effect flex items-center rounded-md border border-border bg-card px-4 py-2 text-card-foreground hover:bg-accent"
            >
              <svg
                className="mr-2 h-5 w-5 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              {intl.formatMessage(
                { id: 'public.contact' },
                { defaultMessage: 'Contact Us' }
              )}
            </Link>
          </div>
        </div>
      </div>

      <div
        ref={categoriesRef}
        data-section="categories"
        className={`section-reveal container mx-auto px-4 py-12 ${visibleSections.categories ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-6xl">
          {/* Category tabs and articles */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
            {/* Category sidebar */}
            <div className="animate-fadeIn lg:col-span-1">
              <h2 className="mb-4 text-xl font-bold">
                {intl.formatMessage(
                  { id: 'public.help.categories' },
                  { defaultMessage: 'Help Topics' }
                )}
              </h2>
              <div className="overflow-hidden rounded-lg border border-border bg-card">
                {HELP_CATEGORIES.map((category, index) => (
                  <button
                    key={category.id}
                    onClick={() => handleCategoryChange(category.id)}
                    className={`stagger-card flex w-full items-center px-4 py-3 text-left transition-colors hover:bg-accent ${category.id === activeCategory ? 'border-l-4 border-primary bg-primary/5' : ''}`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div
                      className={`${category.id === activeCategory ? 'text-primary' : 'text-muted-foreground'} mr-3`}
                    >
                      <svg
                        className="h-8 w-8"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d={category.icon}
                        />
                      </svg>
                    </div>
                    <span
                      className={`${category.id === activeCategory ? 'font-medium text-primary' : 'text-card-foreground'}`}
                    >
                      {category.name[currentLocale]}
                    </span>
                  </button>
                ))}
              </div>

              {/* Popular guides section */}
              <div className="animate-fadeIn animation-delay-300 mt-8">
                <h2 className="mb-4 text-xl font-bold">
                  {intl.formatMessage(
                    { id: 'public.help.guides.title' },
                    { defaultMessage: 'Popular Guides' }
                  )}
                </h2>
                <div className="overflow-hidden rounded-lg border border-border bg-card">
                  {POPULAR_GUIDES.map((guide, index) => (
                    <a
                      key={guide.id}
                      href={`/help/guides/${guide.id}`}
                      className={`stagger-card block p-4 hover:bg-accent ${index !== 0 ? 'border-t border-border' : ''}`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-center">
                        <div className="mr-3 flex-shrink-0 text-primary">
                          <svg
                            className="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d={guide.icon}
                            />
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-card-foreground">
                            {guide.title[currentLocale]}
                          </h3>
                          <p className="mt-1 text-sm text-muted-foreground">
                            {guide.description[currentLocale]}
                          </p>
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </div>

            {/* Articles section */}
            <div className="animate-fadeIn animation-delay-200 lg:col-span-3">
              <div className="rounded-lg border border-border bg-card">
                <div className="border-b border-border p-6">
                  <h2 className="text-2xl font-bold">
                    {
                      HELP_CATEGORIES.find(cat => cat.id === activeCategory)
                        ?.name[currentLocale]
                    }
                  </h2>
                </div>

                <div className="divide-y divide-border">
                  {getActiveArticles().length > 0 ? (
                    getActiveArticles().map((article: any, index: number) => (
                      <a
                        key={article.id}
                        href={`/help/${article.id}`}
                        className="stagger-card block p-6 transition-colors hover:bg-accent"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        <h3 className="flex items-center text-lg font-medium text-foreground">
                          <svg
                            className="mr-2 h-5 w-5 text-primary"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          {article.title[currentLocale]}
                        </h3>
                        {article.summary && (
                          <p className="mt-2 text-sm text-muted-foreground">
                            {article.summary[currentLocale]}
                          </p>
                        )}
                      </a>
                    ))
                  ) : (
                    <div className="p-6 text-center">
                      <p className="text-muted-foreground">
                        {intl.formatMessage(
                          { id: 'public.help.no.results' },
                          {
                            defaultMessage:
                              'No articles found matching your search.',
                          }
                        )}
                      </p>
                      <button
                        onClick={() => setSearchQuery('')}
                        className="mt-2 text-primary hover:underline"
                      >
                        {intl.formatMessage(
                          { id: 'public.help.clear.search' },
                          { defaultMessage: 'Clear search' }
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Resources section */}
              <div
                ref={resourcesRef}
                data-section="resources"
                className={`section-reveal mt-8 ${visibleSections.resources ? 'visible' : ''}`}
              >
                <h2 className="animate-slideUp mb-6 text-2xl font-bold">
                  {intl.formatMessage(
                    { id: 'public.help.resources.title' },
                    { defaultMessage: 'Learning Resources' }
                  )}
                </h2>
                <div className="grid gap-6 md:grid-cols-3">
                  {RESOURCE_TYPES.map((resource, index) => (
                    <a
                      key={resource.id}
                      href={`/help/resources/${resource.id}`}
                      className="stagger-card rounded-lg border border-border bg-card p-6 transition-shadow hover:shadow-sm"
                      style={{ animationDelay: `${index * 150}ms` }}
                    >
                      <div className="mb-4 text-primary">
                        <svg
                          className="h-8 w-8"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={resource.icon}
                          />
                        </svg>
                      </div>
                      <h3 className="mb-2 text-xl font-semibold">
                        {resource.title[currentLocale]}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {resource.description[currentLocale]}
                      </p>
                      <div className="mt-4 inline-flex items-center text-primary">
                        <span className="font-medium">{resource.count}</span>
                        <span className="ml-1 text-sm text-muted-foreground">
                          {intl.formatMessage(
                            { id: 'public.help.resources.available' },
                            { defaultMessage: 'available' }
                          )}
                        </span>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact section */}
      <div
        ref={contactRef}
        data-section="contact"
        className={`section-reveal mt-12 border-t border-border bg-muted py-12 ${visibleSections.contact ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="animate-slideUp mb-2 text-2xl font-bold">
              {intl.formatMessage(
                { id: 'public.help.contact.title' },
                { defaultMessage: 'Still need help?' }
              )}
            </h2>
            <p className="animate-fadeIn animation-delay-200 mb-6 text-muted-foreground">
              {intl.formatMessage(
                { id: 'public.help.contact.description' },
                {
                  defaultMessage:
                    "If you can't find what you're looking for, our support team is here to help.",
                  company: displayName,
                }
              )}
            </p>
            <Link
              to="/contact"
              className="animate-fadeIn animation-delay-300 shimmer-effect inline-flex items-center rounded-md border border-transparent bg-primary px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              {intl.formatMessage(
                { id: 'public.help.contact.button' },
                { defaultMessage: 'Contact Support' }
              )}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
