import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { Link } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import CountUp from 'react-countup';
import { testimonials, getTranslatedText } from '@/data/public/testimonials';
import '@/styles/animations.css'; // Import the centralized animations

/**
 * Home Page Component
 *
 * Main landing page for the portal that showcases the key features
 * and benefits of the platform to attract new users.
 *
 * @returns {JSX.Element} The Home Page component
 */
export default function Page() {
  const intl = useIntl();
  const locale = intl.locale;
  const metadata = PAGE_METADATA.home;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    features: false,
    testimonials: false,
    statistics: false,
    cta: false,
  });

  // Refs for the sections
  const featuresRef = useRef<HTMLDivElement>(null);
  const testimonialsRef = useRef<HTMLDivElement>(null);
  const statisticsRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  // Update document metadata using the metadata helper
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'public.home.title' });
    const translatedDescription = intl.formatMessage({
      id: 'public.home.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (featuresRef.current) observer.observe(featuresRef.current);
    if (testimonialsRef.current) observer.observe(testimonialsRef.current);
    if (statisticsRef.current) observer.observe(statisticsRef.current);
    if (ctaRef.current) observer.observe(ctaRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Features section data
  const features = [
    {
      title: intl.formatMessage({ id: 'public.home.feature1.title' }),
      description: intl.formatMessage({ id: 'public.home.feature1.description' }),
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
          />
        </svg>
      ),
    },
    {
      title: intl.formatMessage({ id: 'public.home.feature2.title' }),
      description: intl.formatMessage({ id: 'public.home.feature2.description' }),
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      ),
    },
    {
      title: intl.formatMessage({ id: 'public.home.feature3.title' }),
      description: intl.formatMessage({ id: 'public.home.feature3.description' }),
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
          />
        </svg>
      ),
    },
  ];

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <div className="container mx-auto px-4 py-20 md:py-32">
          <div className="animate-fadeIn mx-auto max-w-3xl text-center">
            <h1 className="animate-slideUp mb-6 text-4xl font-bold md:text-6xl">
              {intl.formatMessage({ id: 'public.home.title' })}
            </h1>
            <p className="animate-slideUp animation-delay-200 mb-8 text-xl opacity-90 md:text-2xl">
              {displayName} {intl.formatMessage({ id: 'public.home.subtitle' })}
            </p>
            <div className="animate-slideUp animation-delay-400 flex flex-col justify-center gap-4 sm:flex-row">
              <Link
                to="/signup"
                className="shimmer-effect rounded-md bg-white px-8 py-3 font-medium text-primary transition-colors hover:bg-white/90"
              >
                {intl.formatMessage({ id: 'public.home.cta.getStarted' })}
              </Link>
              <Link
                to="/contact"
                className="shimmer-effect rounded-md bg-primary/90 px-8 py-3 font-medium text-white transition-colors hover:bg-primary/70"
              >
                {intl.formatMessage({ id: 'public.home.cta.scheduleDemo' })}
              </Link>
            </div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="relative h-24 bg-background">
          <svg
            className="absolute -top-1 h-24 w-full text-primary"
            preserveAspectRatio="none"
            viewBox="0 0 1440 96"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M0,96 L1440,0 L1440,96 L0,96 Z"></path>
          </svg>
        </div>
      </div>

      {/* Features section */}
      <div
        ref={featuresRef}
        data-section="features"
        className={`section-reveal container mx-auto px-4 py-16 md:py-24 ${visibleSections.features ? 'visible' : ''}`}
      >
        <div className="animate-fadeIn mx-auto mb-16 max-w-3xl text-center">
          <h2 className="mb-4 text-3xl font-bold md:text-4xl">
            {intl.formatMessage({ id: 'public.home.features.title' })}
          </h2>
          <p className="text-xl text-muted-foreground">
            {intl.formatMessage({ id: 'public.home.features.subtitle' })}
          </p>
        </div>

        <div className="mx-auto grid max-w-5xl gap-8 md:grid-cols-3">
          {features.map((feature, index) => (
            <div
              key={index}
              className="animate-scaleIn stagger-card rounded-xl border border-border bg-background p-8 text-center shadow-sm transition-shadow duration-300 hover:shadow-md"
            >
              <div className="mb-4 inline-flex items-center justify-center text-primary">
                {feature.icon}
              </div>
              <h3 className="mb-3 text-xl font-bold">{feature.title}</h3>
              <p className="text-muted-foreground">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Testimonials section */}
      <div
        ref={testimonialsRef}
        data-section="testimonials"
        className={`section-reveal bg-muted/50 py-16 md:py-24 ${visibleSections.testimonials ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="animate-fadeIn mx-auto mb-16 max-w-3xl text-center">
            <h2 className="mb-4 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({ id: 'public.home.testimonials.title' })}
            </h2>
            <p className="text-xl text-muted-foreground">
              {intl.formatMessage({ id: 'public.home.testimonials.subtitle' })}
            </p>
          </div>

          <div className="mx-auto grid max-w-5xl gap-8 md:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className={`card-hover-effect shimmer-effect rounded-xl border border-border bg-background p-6 shadow-sm animate-${index % 2 === 0 ? 'slideInLeft' : 'slideInRight'} stagger-card`}
              >
                <div className="mb-4 flex items-center">
                  <div className="mr-4 flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-muted">
                    {/* In a real app, this would be an actual image */}
                    <span className="text-lg text-muted-foreground">
                      {testimonial.author.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium">{testimonial.author.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {testimonial.author.title} at {testimonial.author.company}
                    </p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "{getTranslatedText(testimonial.content, locale)}"
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Statistics section */}
      <div
        ref={statisticsRef}
        data-section="statistics"
        className={`section-reveal container mx-auto px-4 py-16 md:py-24 ${visibleSections.statistics ? 'visible' : ''}`}
      >
        <div className="mx-auto grid max-w-5xl grid-cols-2 gap-8 text-center md:grid-cols-4">
          <div className="stagger-card animate-fadeIn rounded-xl bg-background p-6">
            <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
              <CountUp end={10} suffix="k+" duration={2.5} enableScrollSpy />
            </div>
            <div className="text-muted-foreground">
              {intl.formatMessage({ id: 'public.home.stats.users' })}
            </div>
          </div>
          <div className="stagger-card animate-fadeIn rounded-xl bg-background p-6">
            <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
              <CountUp end={500} suffix="+" duration={2.5} enableScrollSpy />
            </div>
            <div className="text-muted-foreground">
              {intl.formatMessage({ id: 'public.home.stats.companies' })}
            </div>
          </div>
          <div className="stagger-card animate-fadeIn rounded-xl bg-background p-6">
            <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
              <CountUp end={95} suffix="%" duration={2.5} enableScrollSpy />
            </div>
            <div className="text-muted-foreground">
              {intl.formatMessage({ id: 'public.home.stats.satisfaction' })}
            </div>
          </div>
          <div className="stagger-card animate-fadeIn rounded-xl bg-background p-6">
            <div className="mb-2 text-4xl font-bold text-primary md:text-5xl">
              24/7
            </div>
            <div className="text-muted-foreground">
              {intl.formatMessage({ id: 'public.home.stats.support' })}
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div
        ref={ctaRef}
        data-section="cta"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 py-16 text-white ${visibleSections.cta ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="animate-fadeIn mx-auto max-w-3xl text-center">
            <h2 className="animate-slideUp mb-4 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({ id: 'public.home.cta.title' })}
            </h2>
            <p className="animate-slideUp animation-delay-200 mb-8 text-xl opacity-90">
              {intl.formatMessage(
                { id: 'public.home.cta.subtitle' },
                { company: displayName }
              )}
            </p>
            <div className="animate-slideUp animation-delay-400 flex flex-col justify-center gap-4 sm:flex-row">
              <Link
                to="/signup"
                className="shimmer-effect rounded-md bg-white px-8 py-3 font-medium text-primary transition-colors hover:bg-white/90"
              >
                {intl.formatMessage({ id: 'public.home.cta.trial' })}
              </Link>
              <Link
                to="/contact"
                className="shimmer-effect rounded-md bg-background/10 px-8 py-3 font-medium text-white transition-colors hover:bg-background/20"
              >
                {intl.formatMessage({ id: 'public.home.cta.sales' })}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
