import { useState, useEffect, useRef, JSX } from 'react';
import { PAGE_METADATA } from '@/constants/site-config';

import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import {
  Search,
  Filter,
  Star,
  Zap,
  Users,
  MessageCircle,
  Code,
  BarChart,
  CreditCard,
  Cloud,
  Megaphone,
  CheckCircle,
  Clock,
  AlertCircle,
  BookOpen,
  Settings,
} from 'lucide-react';
import {
  integrations,
  integrationCategories,
  getFeaturedIntegrations,
  getIntegrationsByCategory,
  getIntegrationsByDifficulty,
  getIntegrationsByStatus,
  searchIntegrations,
} from '@/data/public/integrations';
import { Integration, IntegrationFilters } from '@/types/public/integrations';
import { getTranslation } from '@/utils/translation';
import '@/styles/animations.css';

/**
 * Integrations Page Component
 *
 * Displays available integrations with filtering and search functionality.
 * Features include:
 * - Featured integrations section
 * - Category and difficulty filtering
 * - Search functionality
 * - Integration cards with setup guides
 *
 * @returns {JSX.Element} The Integrations Page component
 */
export default function Page(): JSX.Element {
  const intl = useIntl();
  const metadata = PAGE_METADATA.integrations;
  const locale = intl.locale;
  // Helper function to get translated text
  const getText = (text: { en: string; [key: string]: string | undefined }) => {
    return getTranslation(text, locale);
  };

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'integrations.title',
        defaultMessage: 'Integrations',
      }),
      description: intl.formatMessage({
        id: 'integrations.subtitle',
        defaultMessage:
          'Connect with your favorite tools and services to streamline your workflow',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  const [filteredIntegrations, setFilteredIntegrations] =
    useState<Integration[]>(integrations);
  const [filters, setFilters] = useState<IntegrationFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    featured: true,
    filters: true,
    grid: true,
    popular: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const filtersRef = useRef(null);
  const gridRef = useRef(null);
  const popularRef = useRef(null);

  const featuredIntegrations = getFeaturedIntegrations();

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (filtersRef.current) observer.observe(filtersRef.current);
    if (gridRef.current) observer.observe(gridRef.current);
    if (popularRef.current) observer.observe(popularRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);
  useEffect(() => {
    let filtered = [...integrations];

    // Apply search filter
    if (searchQuery) {
      filtered = searchIntegrations(searchQuery);
    }

    // Apply category filter
    if (filters.category) {
      filtered = getIntegrationsByCategory(filters.category);
    }

    // Apply difficulty filter
    if (filters.difficulty) {
      filtered = getIntegrationsByDifficulty(filters.difficulty);
    }

    // Apply status filter
    if (filters.status) {
      filtered = getIntegrationsByStatus(filters.status);
    }

    // Apply featured filter
    if (filters.featured !== undefined) {
      filtered = filtered.filter(
        integration => integration.featured === filters.featured
      );
    }

    // Apply popular filter
    if (filters.popular !== undefined) {
      filtered = filtered.filter(
        integration => integration.popular === filters.popular
      );
    }

    setFilteredIntegrations(filtered);
  }, [searchQuery, filters]);

  const handleFilterChange = (
    key: keyof IntegrationFilters,
    value: string | boolean | undefined
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };
  const getCategoryIcon = (categorySlug: string) => {
    switch (categorySlug) {
      case 'crm':
        return <Users className="h-5 w-5" />;
      case 'communication':
        return <MessageCircle className="h-5 w-5" />;
      case 'development':
        return <Code className="h-5 w-5" />;
      case 'analytics':
        return <BarChart className="h-5 w-5" />;
      case 'payments':
        return <CreditCard className="h-5 w-5" />;
      case 'cloud':
        return <Cloud className="h-5 w-5" />;
      case 'marketing':
        return <Megaphone className="h-5 w-5" />;
      case 'productivity':
        return <Zap className="h-5 w-5" />;
      default:
        return <Settings className="h-5 w-5" />;
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'advanced':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return intl.formatMessage({
          id: 'integrations.difficulty.easy',
          defaultMessage: 'Easy',
        });
      case 'medium':
        return intl.formatMessage({
          id: 'integrations.difficulty.medium',
          defaultMessage: 'Medium',
        });
      case 'advanced':
        return intl.formatMessage({
          id: 'integrations.difficulty.advanced',
          defaultMessage: 'Advanced',
        });
      default:
        return difficulty;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center rounded-full bg-green-500/10 px-2 py-1 text-xs font-medium text-green-600">
            <CheckCircle className="mr-1 h-3 w-3" />
            {intl.formatMessage({
              id: 'integrations.status.active',
              defaultMessage: 'Active',
            })}
          </span>
        );
      case 'beta':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-500/10 px-2 py-1 text-xs font-medium text-yellow-600">
            <Clock className="mr-1 h-3 w-3" />
            {intl.formatMessage({
              id: 'integrations.status.beta',
              defaultMessage: 'Beta',
            })}
          </span>
        );
      case 'coming-soon':
        return (
          <span className="inline-flex items-center rounded-full bg-muted px-2 py-1 text-xs font-medium text-muted-foreground">
            <AlertCircle className="mr-1 h-3 w-3" />
            {intl.formatMessage({
              id: 'integrations.status.coming-soon',
              defaultMessage: 'Coming Soon',
            })}
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'integrations.title',
                defaultMessage: 'Integrations & Connections',
              })}
            </h1>
            <p className="animate-fadeIn animation-delay-200 mb-8 text-xl opacity-90">
              {intl.formatMessage({
                id: 'integrations.subtitle',
                defaultMessage:
                  'Connect your favorite tools and services to streamline your workflow',
              })}
            </p>

            {/* Search Bar */}
            <div className="animate-fadeIn animation-delay-300 relative mx-auto max-w-2xl">
              <div className="absolute left-3 top-3 text-muted-foreground">
                <Search className="h-6 w-6" />
              </div>
              <input
                type="text"
                placeholder={intl.formatMessage({
                  id: 'integrations.search.placeholder',
                  defaultMessage: 'Search integrations...',
                })}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="w-full rounded-lg border border-input bg-background py-3 pl-12 pr-4 text-foreground placeholder:text-muted-foreground focus:border-transparent focus:ring-2 focus:ring-primary"
              />
            </div>
          </div>
        </div>
      </div>
      {/* Featured Integrations */}
      {featuredIntegrations.length > 0 && (
        <div
          ref={featuredRef}
          data-section="featured"
          className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-7xl">
              <div className="animate-slideUp mb-12 text-center">
                <h2 className="mb-4 text-3xl font-bold">
                  {intl.formatMessage({
                    id: 'integrations.featured.title',
                    defaultMessage: 'Featured Integrations',
                  })}
                </h2>
                <p className="mx-auto max-w-2xl text-xl text-muted-foreground">
                  {intl.formatMessage({
                    id: 'integrations.featured.subtitle',
                    defaultMessage:
                      'Our most popular and powerful integrations',
                  })}
                </p>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                {featuredIntegrations.map((integration, index) => (
                  <div
                    key={integration.id}
                    className="stagger-card animate-fadeIn group overflow-hidden rounded-xl bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <div className="p-6">
                      <div className="mb-4 flex items-start justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex h-12 w-12 items-center justify-center overflow-hidden rounded-lg bg-muted">
                            <img
                              src={integration.logo}
                              alt={integration.name}
                              className="h-8 w-8 object-contain"
                            />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-foreground transition-colors group-hover:text-primary">
                              {integration.name}
                            </h3>
                            <div className="mt-1 flex items-center gap-2">
                              <div className="rounded bg-primary/10 p-1 text-primary">
                                {getCategoryIcon(integration.category.slug)}
                              </div>
                              <span className="text-sm font-medium text-primary">
                                {getText(integration.category.name)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-2">
                          {integration.featured && (
                            <span className="flex items-center gap-1 rounded-full bg-yellow-500/10 px-2 py-1 text-xs font-medium text-yellow-600">
                              <Star className="h-3 w-3" />
                              {intl.formatMessage({
                                id: 'integrations.featured.badge',
                                defaultMessage: 'Featured',
                              })}
                            </span>
                          )}
                          {getStatusBadge(integration.status)}
                        </div>
                      </div>

                      <p className="mb-4 line-clamp-3 text-muted-foreground">
                        {getText(integration.shortDescription)}
                      </p>

                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          {getDifficultyIcon(integration.setupDifficulty)}
                          <span>
                            {getDifficultyText(integration.setupDifficulty)}
                          </span>
                        </div>
                        {integration.integrationCount && (
                          <div className="text-sm text-muted-foreground">
                            {intl.formatMessage(
                              {
                                id: 'public.integrations.installs.count',
                                defaultMessage: '{count} installs',
                              },
                              {
                                count:
                                  integration.integrationCount.toLocaleString(),
                              }
                            )}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        {integration.documentationUrl && (
                          <a
                            href={integration.documentationUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="shimmer-effect inline-flex items-center gap-2 font-medium text-primary transition-colors hover:text-primary/80"
                          >
                            <BookOpen className="h-4 w-4" />
                            {intl.formatMessage({
                              id: 'integrations.view.docs',
                              defaultMessage: 'View Docs',
                            })}
                          </a>
                        )}

                        {integration.setupGuideUrl && (
                          <a
                            href={integration.setupGuideUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="shimmer-effect inline-flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition-colors hover:bg-primary/90"
                          >
                            <Settings className="h-4 w-4" />
                            {intl.formatMessage({
                              id: 'integrations.setup',
                              defaultMessage: 'Setup',
                            })}
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Integrations Grid */}
      <div
        ref={filtersRef}
        data-section="filters"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.filters ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          {/* Filter Controls */}
          <div className="animate-fadeIn mb-8">
            <div className="mb-6 flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              <h2 className="text-2xl font-bold text-foreground">
                {intl.formatMessage({
                  id: 'integrations.all.title',
                  defaultMessage: 'All Integrations',
                })}
              </h2>

              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="shimmer-effect inline-flex items-center gap-2 rounded-lg border border-border px-4 py-2 text-foreground transition-colors hover:bg-accent"
                >
                  <Filter className="h-4 w-4" />
                  {intl.formatMessage({
                    id: 'integrations.filters',
                    defaultMessage: 'Filters',
                  })}
                </button>

                {(Object.keys(filters).length > 0 || searchQuery) && (
                  <button
                    onClick={clearFilters}
                    className="shimmer-effect font-medium text-primary transition-colors hover:text-primary/80"
                  >
                    {intl.formatMessage({
                      id: 'integrations.clear.filters',
                      defaultMessage: 'Clear Filters',
                    })}
                  </button>
                )}
              </div>
            </div>

            {/* Filter Panel */}
            {showFilters && (
              <div className="animate-fadeIn mb-6 rounded-xl bg-muted p-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {/* Category Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'integrations.filter.category',
                        defaultMessage: 'Category',
                      })}
                    </label>
                    <select
                      value={filters.category || ''}
                      onChange={e =>
                        handleFilterChange(
                          'category',
                          e.target.value || undefined
                        )
                      }
                      className="w-full rounded-lg border border-border bg-background px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">
                        {intl.formatMessage({
                          id: 'integrations.filter.all.categories',
                          defaultMessage: 'All Categories',
                        })}
                      </option>
                      {integrationCategories.map(category => (
                        <option key={category.id} value={category.slug}>
                          {getText(category.name)}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Difficulty Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'integrations.filter.difficulty',
                        defaultMessage: 'Difficulty',
                      })}
                    </label>
                    <select
                      value={filters.difficulty || ''}
                      onChange={e =>
                        handleFilterChange(
                          'difficulty',
                          e.target.value || undefined
                        )
                      }
                      className="w-full rounded-lg border border-border bg-background px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">
                        {intl.formatMessage({
                          id: 'integrations.filter.all.difficulties',
                          defaultMessage: 'All Difficulties',
                        })}
                      </option>
                      <option value="easy">
                        {intl.formatMessage({
                          id: 'integrations.difficulty.easy',
                          defaultMessage: 'Easy',
                        })}
                      </option>
                      <option value="medium">
                        {intl.formatMessage({
                          id: 'integrations.difficulty.medium',
                          defaultMessage: 'Medium',
                        })}
                      </option>
                      <option value="advanced">
                        {intl.formatMessage({
                          id: 'integrations.difficulty.advanced',
                          defaultMessage: 'Advanced',
                        })}
                      </option>
                    </select>
                  </div>

                  {/* Status Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'integrations.filter.status',
                        defaultMessage: 'Status',
                      })}
                    </label>
                    <select
                      value={filters.status || ''}
                      onChange={e =>
                        handleFilterChange(
                          'status',
                          e.target.value || undefined
                        )
                      }
                      className="w-full rounded-lg border border-border bg-background px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">
                        {intl.formatMessage({
                          id: 'integrations.filter.all.statuses',
                          defaultMessage: 'All Statuses',
                        })}
                      </option>
                      <option value="active">
                        {intl.formatMessage({
                          id: 'integrations.status.active',
                          defaultMessage: 'Active',
                        })}
                      </option>
                      <option value="beta">
                        {intl.formatMessage({
                          id: 'integrations.status.beta',
                          defaultMessage: 'Beta',
                        })}
                      </option>
                      <option value="coming-soon">
                        {intl.formatMessage({
                          id: 'integrations.status.coming-soon',
                          defaultMessage: 'Coming Soon',
                        })}
                      </option>
                    </select>
                  </div>

                  {/* Popular Filter */}
                  <div>
                    <label className="mb-2 block text-sm font-medium text-foreground">
                      {intl.formatMessage({
                        id: 'integrations.filter.type',
                        defaultMessage: 'Type',
                      })}
                    </label>
                    <select
                      value={
                        filters.popular === undefined
                          ? ''
                          : filters.popular
                            ? 'popular'
                            : 'all'
                      }
                      onChange={e => {
                        const value = e.target.value;
                        handleFilterChange(
                          'popular',
                          value === '' ? undefined : value === 'popular'
                        );
                      }}
                      className="w-full rounded-lg border border-border bg-background px-3 py-2 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    >
                      <option value="">
                        {intl.formatMessage({
                          id: 'integrations.filter.all.types',
                          defaultMessage: 'All Types',
                        })}
                      </option>
                      <option value="popular">
                        {intl.formatMessage({
                          id: 'integrations.filter.popular',
                          defaultMessage: 'Popular Only',
                        })}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Results Count */}
            <div className="mb-6 text-gray-600 dark:text-gray-400">
              {intl.formatMessage(
                {
                  id: 'integrations.results.count',
                  defaultMessage:
                    'Showing {count} {count, plural, one {integration} other {integrations}}',
                },
                { count: filteredIntegrations.length }
              )}
            </div>
          </div>
          {/* Integrations Grid */}
          <div
            ref={gridRef}
            data-section="grid"
            className={`animate-fadeIn animation-delay-300 ${
              visibleSections.grid
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            }`}
          >
            {filteredIntegrations.length > 0 && (
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredIntegrations.map((integration, index) => (
                  <div
                    key={integration.id}
                    className="stagger-card animate-fadeIn group overflow-hidden rounded-xl bg-card shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="p-6">
                      <div className="mb-4 flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-lg bg-muted">
                            <img
                              src={integration.logo}
                              alt={integration.name}
                              className="h-6 w-6 object-contain"
                            />
                          </div>
                          <div>
                            <h3 className="font-bold text-foreground transition-colors group-hover:text-primary">
                              {integration.name}
                            </h3>
                            <div className="mt-1 flex items-center gap-1">
                              <div className="rounded bg-primary/10 p-1 text-xs text-primary">
                                {getCategoryIcon(integration.category.slug)}
                              </div>
                              <span className="text-xs font-medium text-primary">
                                {getText(integration.category.name)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-1">
                          {integration.popular && (
                            <span className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                              {intl.formatMessage({
                                id: 'integrations.popular.badge',
                                defaultMessage: 'Popular',
                              })}
                            </span>
                          )}
                          {getStatusBadge(integration.status)}
                        </div>
                      </div>

                      <p className="mb-4 line-clamp-2 text-sm text-muted-foreground">
                        {getText(integration.shortDescription)}
                      </p>

                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          {getDifficultyIcon(integration.setupDifficulty)}
                          <span>
                            {getDifficultyText(integration.setupDifficulty)}
                          </span>
                        </div>
                        {integration.integrationCount && (
                          <div className="text-sm text-muted-foreground">
                            {integration.integrationCount.toLocaleString()}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        {integration.documentationUrl && (
                          <a
                            href={integration.documentationUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="shimmer-effect inline-flex items-center gap-1 text-sm font-medium text-primary transition-colors hover:text-primary/80"
                          >
                            <BookOpen className="h-4 w-4" />
                            {intl.formatMessage({
                              id: 'integrations.docs',
                              defaultMessage: 'Docs',
                            })}
                          </a>
                        )}

                        {integration.setupGuideUrl && (
                          <a
                            href={integration.setupGuideUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="shimmer-effect inline-flex items-center gap-1 rounded bg-primary px-3 py-1 text-sm text-white transition-colors hover:bg-primary/90"
                          >
                            <Settings className="h-4 w-4" />
                            {intl.formatMessage({
                              id: 'integrations.setup',
                              defaultMessage: 'Setup',
                            })}
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
