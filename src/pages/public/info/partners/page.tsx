import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { ExternalLink, Quote, Users, Globe, Award } from 'lucide-react';
import { Button } from '@/components/ui/shadcn/button';
import { Card, CardContent } from '@/components/ui/shadcn/card';
import { Badge } from '@/components/ui/shadcn/badge';
import {
  partners,
  partnerCategories,
  getFeaturedPartners,
  getPartnersByCategory,
} from '@/data/public/partners';
import { Partner } from '@/types/public/partners';
import '@/styles/animations.css';

/**
 * Partners Page Component
 *
 * Displays partner showcase with categories and testimonials.
 * Features include:
 * - Featured partners section
 * - Category filtering
 * - Partner cards with logos and descriptions
 * - Testimonials and case study links
 *
 * @returns {JSX.Element} The Partners Page component
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.partners;
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredPartners, setFilteredPartners] = useState<Partner[]>(partners);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    featured: true,
    categories: true,
    grid: true,
    cta: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const featuredRef = useRef(null);
  const categoriesRef = useRef(null);
  const gridRef = useRef(null);
  const ctaRef = useRef(null);

  const featuredPartners = getFeaturedPartners();

  // Update document metadata
  useEffect(() => {
    const translatedMetadata = {
      ...metadata,
      title: intl.formatMessage({
        id: 'public.partners.title',
        defaultMessage: 'Our Partners',
      }),
      description: intl.formatMessage({
        id: 'public.partners.subtitle',
        defaultMessage:
          'Discover our trusted partners who help us deliver exceptional solutions and services',
      }),
    };
    updatePageMetadata(translatedMetadata);
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (featuredRef.current) observer.observe(featuredRef.current);
    if (categoriesRef.current) observer.observe(categoriesRef.current);
    if (gridRef.current) observer.observe(gridRef.current);
    if (ctaRef.current) observer.observe(ctaRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    setFilteredPartners(getPartnersByCategory(selectedCategory));
  }, [selectedCategory]);

  const getTranslatedText = (text: any, locale: string) => {
    if (typeof text === 'object' && text !== null) {
      return text[locale] || text.en || '';
    }
    return text || '';
  };

  return (
    <div className="bg-background">
      {/* Hero Section */}
      <div
        ref={heroRef}
        data-section="hero"
        className={`section-reveal bg-gradient-to-r from-primary to-primary/80 text-white ${visibleSections.hero ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="animate-hero-content mx-auto max-w-4xl text-center">
            <h1 className="animate-fadeIn mb-6 text-4xl font-bold">
              {intl.formatMessage({
                id: 'public.partners.title',
                defaultMessage: 'Our Partners',
              })}
            </h1>
            <p className="hero-element hero-delay-1 mb-12 text-xl opacity-90">
              {intl.formatMessage({
                id: 'public.partners.subtitle',
                defaultMessage:
                  'Discover our trusted partners who help us deliver exceptional solutions and services to customers worldwide',
              })}
            </p>

            {/* Stats */}
            <div className="hero-element hero-delay-2 mx-auto grid max-w-4xl grid-cols-1 gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <div className="mb-2 text-3xl font-bold text-white">
                  {partners.length}+
                </div>
                <div className="text-white/80">
                  {intl.formatMessage({
                    id: 'public.partners.stats.partners',
                    defaultMessage: 'Global Partners',
                  })}
                </div>
              </div>
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <div className="mb-2 text-3xl font-bold text-white">50+</div>
                <div className="text-white/80">
                  {intl.formatMessage({
                    id: 'public.partners.stats.countries',
                    defaultMessage: 'Countries Served',
                  })}
                </div>
              </div>
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                  <Award className="h-8 w-8 text-white" />
                </div>
                <div className="mb-2 text-3xl font-bold text-white">98%</div>
                <div className="text-white/80">
                  {intl.formatMessage({
                    id: 'public.partners.stats.satisfaction',
                    defaultMessage: 'Partner Satisfaction',
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Partners */}
      {featuredPartners.length > 0 && (
        <div
          ref={featuredRef}
          data-section="featured"
          className={`section-reveal bg-muted py-16 ${visibleSections.featured ? 'visible' : ''}`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-7xl">
              <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold md:text-4xl">
                {intl.formatMessage({
                  id: 'public.partners.featured.title',
                  defaultMessage: 'Featured Partners',
                })}
              </h2>
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {featuredPartners.slice(0, 4).map((partner, index) => (
                  <Card
                    key={partner.id}
                    className="stagger-card animate-fadeIn overflow-hidden transition-all duration-300 hover:shadow-xl"
                    style={{ animationDelay: `${index * 150}ms` }}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <img
                          src={partner.logo}
                          alt={partner.name}
                          className="h-16 w-16 rounded-lg bg-muted object-contain p-2"
                        />
                        <div className="flex-1">
                          <div className="mb-2 flex items-center justify-between">
                            <h3 className="text-xl font-bold text-foreground">
                              {partner.name}
                            </h3>
                            <Badge variant="secondary">
                              {getTranslatedText(
                                partner.category.name,
                                intl.locale
                              )}
                            </Badge>
                          </div>
                          <p className="mb-4 text-muted-foreground">
                            {getTranslatedText(
                              partner.description,
                              intl.locale
                            )}
                          </p>

                          {partner.testimonial && (
                            <div className="mb-4 rounded-lg bg-muted/50 p-4">
                              <Quote className="mb-2 h-5 w-5 text-primary" />
                              <p className="mb-2 text-sm italic text-foreground">
                                "
                                {getTranslatedText(
                                  partner.testimonial.quote,
                                  intl.locale
                                )}
                                "
                              </p>
                              <div className="text-xs text-muted-foreground">
                                <strong>{partner.testimonial.author}</strong>,{' '}
                                {partner.testimonial.position}
                              </div>
                            </div>
                          )}

                          <div className="flex items-center space-x-3">
                            <Button
                              variant="outline"
                              size="sm"
                              className="shimmer-effect"
                              asChild
                            >
                              <a
                                href={partner.website}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <ExternalLink className="mr-2 h-4 w-4" />
                                {intl.formatMessage({
                                  id: 'public.partners.visit',
                                  defaultMessage: 'Visit Website',
                                })}
                              </a>
                            </Button>
                            {partner.caseStudyLink && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="shimmer-effect"
                                asChild
                              >
                                <a href={partner.caseStudyLink}>
                                  {intl.formatMessage({
                                    id: 'public.partners.caseStudy',
                                    defaultMessage: 'Case Study',
                                  })}
                                </a>
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Category Filter */}
      <div
        ref={categoriesRef}
        data-section="categories"
        className={`section-reveal container mx-auto px-4 py-8 ${visibleSections.categories ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <div className="animate-fadeIn flex flex-wrap justify-center gap-4">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              onClick={() => setSelectedCategory('all')}
              className="shimmer-effect transition-all duration-200"
            >
              {intl.formatMessage({
                id: 'public.partners.category.all',
                defaultMessage: 'All Partners',
              })}
            </Button>
            {partnerCategories.map(category => (
              <Button
                key={category.id}
                variant={
                  selectedCategory === category.slug ? 'default' : 'outline'
                }
                onClick={() => setSelectedCategory(category.slug)}
                className="shimmer-effect transition-all duration-200"
              >
                {getTranslatedText(category.name, intl.locale)}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Partners Grid */}
      <div
        ref={gridRef}
        data-section="grid"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.grid ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-7xl">
          <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold md:text-4xl">
            {intl.formatMessage({
              id: 'public.partners.all.title',
              defaultMessage: 'All Partners',
            })}
          </h2>

          {filteredPartners.length === 0 ? (
            <div className="animate-fadeIn py-12 text-center">
              <p className="text-lg text-muted-foreground">
                {intl.formatMessage({
                  id: 'public.partners.no.results',
                  defaultMessage: 'No partners found in this category.',
                })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredPartners.map((partner, index) => (
                <Card
                  key={partner.id}
                  className="stagger-card animate-fadeIn overflow-hidden transition-all duration-300 hover:shadow-xl"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <CardContent className="p-6">
                    <div className="mb-4 text-center">
                      <img
                        src={partner.logo}
                        alt={partner.name}
                        className="mx-auto h-20 w-20 rounded-lg bg-muted object-contain p-3"
                      />
                    </div>
                    <div className="mb-4 text-center">
                      <h3 className="mb-2 text-lg font-bold text-foreground">
                        {partner.name}
                      </h3>
                      <Badge variant="secondary" className="mb-3">
                        {getTranslatedText(partner.category.name, intl.locale)}
                      </Badge>
                      <p className="text-sm text-muted-foreground">
                        {getTranslatedText(partner.description, intl.locale)}
                      </p>
                    </div>

                    <div className="flex justify-center">
                      <Button
                        variant="outline"
                        size="sm"
                        className="shimmer-effect"
                        asChild
                      >
                        <a
                          href={partner.website}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <ExternalLink className="mr-2 h-4 w-4" />
                          {intl.formatMessage({
                            id: 'public.partners.visit',
                            defaultMessage: 'Visit Website',
                          })}
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary/5 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="animate-slideUp mb-6 text-3xl font-bold md:text-4xl">
              {intl.formatMessage({
                id: 'public.partners.cta.title',
                defaultMessage: 'Become a Partner',
              })}
            </h2>
            <p className="animate-slideUp animation-delay-200 mx-auto mb-8 max-w-2xl text-xl text-muted-foreground">
              {intl.formatMessage({
                id: 'public.partners.cta.description',
                defaultMessage:
                  'Join our partner ecosystem and grow your business with us',
              })}
            </p>
            <div className="animate-fadeIn animation-delay-300 flex flex-col justify-center gap-4 sm:flex-row">
              <Button size="lg" className="shimmer-effect">
                {intl.formatMessage({
                  id: 'public.partners.cta.contact',
                  defaultMessage: 'Contact Us',
                })}
              </Button>
              <Button variant="outline" size="lg" className="shimmer-effect">
                {intl.formatMessage({
                  id: 'public.partners.cta.learnMore',
                  defaultMessage: 'Learn More',
                })}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
