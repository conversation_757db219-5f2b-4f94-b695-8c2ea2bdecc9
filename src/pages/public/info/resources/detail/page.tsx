import { PAGE_METADATA } from '@/constants/site-config';
import { useState, useEffect, useRef } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { Link, useParams, Navigate } from 'react-router-dom';
import {
  ArrowLeft,
  Download,
  ExternalLink,
  Star,
  Tag,
  FileText,
  Book,
  LayoutTemplate,
  Compass,
  Video,
  Monitor,
  Lock,
  Calendar,
  Share2,
  ChevronRight,
} from 'lucide-react';
import {
  getResourceBySlug,
  getRelatedResources,
} from '@/data/public/resources';
import { Resource } from '@/types/public/resources';
import '@/styles/animations.css';

/**
 * Resource Detail Page Component
 *
 * Displays detailed information about a specific resource.
 * Features include:
 * - Resource details and metadata
 * - Download/view functionality
 * - Related resources
 * - Author information
 *
 * @returns {JSX.Element} The Resource Detail Page component
 */
export default function Page() {
  const intl = useIntl();
  const { slug } = useParams<{ slug: string }>();
  const metadata = PAGE_METADATA.resources;

  const [resource, setResource] = useState<Resource | null>(null);
  const [relatedResources, setRelatedResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    hero: true,
    content: true,
    related: true,
  });

  // Refs for the sections
  const heroRef = useRef(null);
  const contentRef = useRef(null);
  const relatedRef = useRef(null);

  useEffect(() => {
    if (slug) {
      const foundResource = getResourceBySlug(slug);
      if (foundResource) {
        setResource(foundResource);
        setRelatedResources(getRelatedResources(foundResource));
      }
      setLoading(false);
    }
  }, [slug]);

  // Update document metadata
  useEffect(() => {
    if (resource) {
      const translatedMetadata = {
        ...metadata,
        title: resource.title.en,
        description: resource.description.en,
      };
      updatePageMetadata(translatedMetadata);
    }
  }, [resource, metadata]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (heroRef.current) observer.observe(heroRef.current);
    if (contentRef.current) observer.observe(contentRef.current);
    if (relatedRef.current) observer.observe(relatedRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!resource) {
    return <Navigate to="/resources" replace />;
  }

  const getCategoryIcon = (categorySlug: string) => {
    switch (categorySlug) {
      case 'whitepapers':
        return <FileText className="h-5 w-5" />;
      case 'ebooks':
        return <Book className="h-5 w-5" />;
      case 'templates':
        return <LayoutTemplate className="h-5 w-5" />;
      case 'guides':
        return <Compass className="h-5 w-5" />;
      case 'videos':
        return <Video className="h-5 w-5" />;
      case 'webinars':
        return <Monitor className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const getFileTypeIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf':
      case 'doc':
        return <FileText className="h-4 w-4" />;
      case 'video':
        return <Video className="h-4 w-4" />;
      case 'xls':
        return <LayoutTemplate className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: resource.title.en,
          text: resource.description.en,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <section
        ref={heroRef}
        data-section="hero"
        className={`relative py-20 transition-all duration-1000 ${
          visibleSections.hero
            ? 'translate-y-0 opacity-100'
            : 'translate-y-10 opacity-0'
        }`}
      >
        <div className="container mx-auto px-4">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Link
                to="/resources"
                className="transition-colors hover:text-blue-600"
              >
                {intl.formatMessage({
                  id: 'public.resources.title',
                  defaultMessage: 'Resources',
                })}
              </Link>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-900">{resource.title.en}</span>
            </div>
          </nav>

          <div className="mx-auto max-w-6xl">
            <div className="grid grid-cols-1 gap-12 lg:grid-cols-3">
              {/* Main Content */}
              <div className="lg:col-span-2">
                {/* Back Button */}
                <Link
                  to="/resources"
                  className="mb-6 inline-flex items-center gap-2 font-medium text-blue-600 transition-colors hover:text-blue-700"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {intl.formatMessage({
                    id: 'common.back.to.resources',
                    defaultMessage: 'Back to Resources',
                  })}
                </Link>

                {/* Resource Header */}
                <div className="mb-8">
                  <div className="mb-4 flex items-center gap-3">
                    <div
                      className="rounded-lg p-3"
                      style={{
                        backgroundColor: `${resource.category.color}20`,
                        color: resource.category.color,
                      }}
                    >
                      {getCategoryIcon(resource.category.slug)}
                    </div>
                    <div>
                      <span
                        className="text-sm font-medium"
                        style={{ color: resource.category.color }}
                      >
                        {resource.category.name.en}
                      </span>
                      <div className="mt-1 flex items-center gap-2">
                        {resource.featured && (
                          <span className="flex items-center gap-1 rounded-full bg-yellow-400 px-2 py-1 text-xs font-medium text-yellow-900">
                            <Star className="h-3 w-3" />
                            {intl.formatMessage({
                              id: 'public.resources.featured.badge',
                              defaultMessage: 'Featured',
                            })}
                          </span>
                        )}
                        {resource.premium && (
                          <span className="flex items-center gap-1 rounded-full bg-purple-600 px-2 py-1 text-xs font-medium text-white">
                            <Lock className="h-3 w-3" />
                            {intl.formatMessage({
                              id: 'public.resources.premium.badge',
                              defaultMessage: 'Premium',
                            })}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <h1 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
                    {resource.title.en}
                  </h1>

                  <p className="mb-6 text-xl text-gray-600">
                    {resource.description.en}
                  </p>

                  {/* Resource Meta */}
                  <div className="mb-6 flex flex-wrap items-center gap-6 text-sm text-gray-500">
                    <div className="flex items-center gap-2">
                      {getFileTypeIcon(resource.fileType)}
                      <span className="font-medium uppercase">
                        {resource.fileType}
                      </span>
                      {resource.fileSize && (
                        <>
                          <span>•</span>
                          <span>{resource.fileSize}</span>
                        </>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>
                        {intl.formatMessage({
                          id: 'public.resources.published',
                          defaultMessage: 'Published',
                        })}{' '}
                        {new Date(resource.publishedAt).toLocaleDateString()}
                      </span>
                    </div>

                    {resource.downloadCount && (
                      <div className="flex items-center gap-2">
                        <Download className="h-4 w-4" />
                        <span>
                          {resource.downloadCount.toLocaleString()} downloads
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Tags */}
                  {resource.tags.length > 0 && (
                    <div className="mb-6 flex flex-wrap gap-2">
                      {resource.tags.map(tag => (
                        <span
                          key={tag.id}
                          className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                        >
                          <Tag className="h-3 w-3" />
                          {tag.name.en}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex flex-wrap items-center gap-4">
                    {resource.downloadUrl ? (
                      <a
                        href={resource.downloadUrl}
                        className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
                        download
                      >
                        <Download className="h-5 w-5" />
                        {intl.formatMessage({
                          id: 'public.resources.download',
                          defaultMessage: 'Download',
                        })}
                      </a>
                    ) : resource.externalUrl ? (
                      <a
                        href={resource.externalUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
                      >
                        <ExternalLink className="h-5 w-5" />
                        {intl.formatMessage({
                          id: 'public.resources.view',
                          defaultMessage: 'View',
                        })}
                      </a>
                    ) : null}

                    <button
                      onClick={handleShare}
                      className="inline-flex items-center gap-2 rounded-lg border border-gray-300 px-6 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                    >
                      <Share2 className="h-5 w-5" />
                      {intl.formatMessage({
                        id: 'common.share',
                        defaultMessage: 'Share',
                      })}
                    </button>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                {/* Featured Image */}
                {resource.featuredImage && (
                  <div className="mb-8">
                    <img
                      src={resource.featuredImage}
                      alt={resource.title.en}
                      className="h-64 w-full rounded-xl object-cover shadow-lg"
                    />
                  </div>
                )}

                {/* Author Info */}
                {resource.author && (
                  <div className="mb-8 rounded-xl bg-white p-6 shadow-lg">
                    <h3 className="mb-4 text-lg font-semibold text-gray-900">
                      {intl.formatMessage({
                        id: 'public.resources.author',
                        defaultMessage: 'Author',
                      })}
                    </h3>

                    <div className="flex items-start gap-4">
                      {resource.author.avatar && (
                        <img
                          src={resource.author.avatar}
                          alt={resource.author.name}
                          className="h-12 w-12 rounded-full object-cover"
                        />
                      )}

                      <div className="flex-1">
                        <h4 className="mb-1 font-semibold text-gray-900">
                          {resource.author.name}
                        </h4>

                        {resource.author.title && (
                          <p className="mb-2 text-sm text-gray-600">
                            {resource.author.title.en}
                          </p>
                        )}

                        {resource.author.bio && (
                          <p className="text-sm text-gray-600">
                            {resource.author.bio.en}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Resource Stats */}
                <div className="rounded-xl bg-white p-6 shadow-lg">
                  <h3 className="mb-4 text-lg font-semibold text-gray-900">
                    {intl.formatMessage({
                      id: 'public.resources.details',
                      defaultMessage: 'Resource Details',
                    })}
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">
                        {intl.formatMessage({
                          id: 'public.resources.category',
                          defaultMessage: 'Category',
                        })}
                      </span>
                      <span className="font-medium text-gray-900">
                        {resource.category.name.en}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">
                        {intl.formatMessage({
                          id: 'public.resources.file.type',
                          defaultMessage: 'File Type',
                        })}
                      </span>
                      <span className="font-medium uppercase text-gray-900">
                        {resource.fileType}
                      </span>
                    </div>

                    {resource.fileSize && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">
                          {intl.formatMessage({
                            id: 'public.resources.file.size',
                            defaultMessage: 'File Size',
                          })}
                        </span>
                        <span className="font-medium text-gray-900">
                          {resource.fileSize}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">
                        {intl.formatMessage({
                          id: 'public.resources.access',
                          defaultMessage: 'Access',
                        })}
                      </span>
                      <span
                        className={`font-medium ${resource.premium ? 'text-purple-600' : 'text-green-600'}`}
                      >
                        {resource.premium
                          ? intl.formatMessage({
                              id: 'public.resources.premium',
                              defaultMessage: 'Premium',
                            })
                          : intl.formatMessage({
                              id: 'public.resources.free',
                              defaultMessage: 'Free',
                            })}
                      </span>
                    </div>

                    {resource.downloadCount && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">
                          {intl.formatMessage({
                            id: 'public.resources.downloads',
                            defaultMessage: 'Downloads',
                          })}
                        </span>
                        <span className="font-medium text-gray-900">
                          {resource.downloadCount.toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section
        ref={contentRef}
        data-section="content"
        className={`bg-white py-16 transition-all delay-200 duration-1000 ${
          visibleSections.content
            ? 'translate-y-0 opacity-100'
            : 'translate-y-10 opacity-0'
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            {resource.content ? (
              <div className="prose prose-lg max-w-none">
                <div
                  dangerouslySetInnerHTML={{ __html: resource.content.en }}
                />
              </div>
            ) : (
              <div className="py-12 text-center">
                <div className="mb-4 text-gray-400">
                  <FileText className="mx-auto h-16 w-16" />
                </div>
                <h3 className="mb-2 text-xl font-semibold text-gray-900">
                  {intl.formatMessage({
                    id: 'public.resources.preview.not.available',
                    defaultMessage: 'Preview not available',
                  })}
                </h3>
                <p className="mb-6 text-gray-600">
                  {intl.formatMessage({
                    id: 'public.resources.download.to.view',
                    defaultMessage:
                      'Download the resource to view its full content.',
                  })}
                </p>

                {resource.downloadUrl && (
                  <a
                    href={resource.downloadUrl}
                    className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
                    download
                  >
                    <Download className="h-5 w-5" />
                    {intl.formatMessage({
                      id: 'public.resources.download',
                      defaultMessage: 'Download',
                    })}
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Related Resources */}
      {relatedResources.length > 0 && (
        <section
          ref={relatedRef}
          data-section="related"
          className={`delay-400 bg-gray-50 py-16 transition-all duration-1000 ${
            visibleSections.related
              ? 'translate-y-0 opacity-100'
              : 'translate-y-10 opacity-0'
          }`}
        >
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-6xl">
              <h2 className="mb-8 text-center text-2xl font-bold text-gray-900 md:text-3xl">
                {intl.formatMessage({
                  id: 'public.resources.related.title',
                  defaultMessage: 'Related Resources',
                })}
              </h2>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                {relatedResources.map((relatedResource, index) => (
                  <Link
                    key={relatedResource.id}
                    to={`/resources/${relatedResource.slug}`}
                    className={`animate-fade-in-up group overflow-hidden rounded-xl bg-white shadow-lg transition-all duration-300 hover:shadow-xl`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {relatedResource.featuredImage && (
                      <div className="relative h-48 overflow-hidden">
                        <img
                          src={relatedResource.featuredImage}
                          alt={relatedResource.title.en}
                          className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        {relatedResource.featured && (
                          <div className="absolute left-4 top-4">
                            <span className="flex items-center gap-1 rounded-full bg-yellow-400 px-3 py-1 text-sm font-medium text-yellow-900">
                              <Star className="h-3 w-3" />
                              {intl.formatMessage({
                                id: 'public.resources.featured.badge',
                                defaultMessage: 'Featured',
                              })}
                            </span>
                          </div>
                        )}
                        {relatedResource.premium && (
                          <div className="absolute right-4 top-4">
                            <span className="flex items-center gap-1 rounded-full bg-purple-600 px-3 py-1 text-sm font-medium text-white">
                              <Lock className="h-3 w-3" />
                              {intl.formatMessage({
                                id: 'public.resources.premium.badge',
                                defaultMessage: 'Premium',
                              })}
                            </span>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="p-6">
                      <div className="mb-3 flex items-center gap-2">
                        <div
                          className="rounded-lg p-2"
                          style={{
                            backgroundColor: `${relatedResource.category.color}20`,
                            color: relatedResource.category.color,
                          }}
                        >
                          {getCategoryIcon(relatedResource.category.slug)}
                        </div>
                        <span
                          className="text-sm font-medium"
                          style={{ color: relatedResource.category.color }}
                        >
                          {relatedResource.category.name.en}
                        </span>
                      </div>

                      <h3 className="mb-3 text-xl font-bold text-gray-900 transition-colors group-hover:text-blue-600">
                        {relatedResource.title.en}
                      </h3>

                      <p className="mb-4 line-clamp-3 text-gray-600">
                        {relatedResource.excerpt.en}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          {getFileTypeIcon(relatedResource.fileType)}
                          <span className="uppercase">
                            {relatedResource.fileType}
                          </span>
                          {relatedResource.fileSize && (
                            <>
                              <span>•</span>
                              <span>{relatedResource.fileSize}</span>
                            </>
                          )}
                        </div>

                        {relatedResource.downloadCount && (
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Download className="h-4 w-4" />
                            <span>
                              {relatedResource.downloadCount.toLocaleString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
