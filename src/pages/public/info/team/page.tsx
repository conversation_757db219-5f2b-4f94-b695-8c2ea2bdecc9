import { SITE_INFO, PAGE_METADATA } from '@/constants/site-config';
import { useEffect, useRef, useState } from 'react';
import { updatePageMetadata } from '@/utils';
import { useIntl } from 'react-intl';
import { teamMembers, departments } from '@/data/public/team';
import { getTranslation } from '@/utils/translation';
import '@/styles/animations.css';

/**
 * Team Page
 *
 * Displays information about team members and the company structure.
 * This page showcases the leadership team and key personnel.
 */
export default function Page() {
  const intl = useIntl();
  const metadata = PAGE_METADATA.team;
  const companyName = SITE_INFO.organization.name;
  const displayName = SITE_INFO.organization.displayName || companyName;
  const locale = intl.locale;

  // Helper function to get translated text
  const getText = (
    text: string | { en: string; [key: string]: string | undefined }
  ) => {
    return getTranslation(text, locale);
  };

  // State to track which sections are visible
  const [visibleSections, setVisibleSections] = useState({
    leadership: false,
    team: false,
    departments: false,
    join: false,
  });

  // Refs for the sections
  const leadershipRef = useRef(null);
  const teamRef = useRef(null);
  const departmentsRef = useRef(null);
  const joinRef = useRef(null);

  // Update document metadata
  useEffect(() => {
    const translatedTitle = intl.formatMessage({ id: 'public.team.title' });
    const translatedDescription = intl.formatMessage({
      id: 'public.team.subtitle',
    });

    updatePageMetadata({
      ...metadata,
      title: translatedTitle,
      description: translatedDescription,
    });
  }, [metadata, intl]);

  // Add intersection observer to check when sections are visible
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.getAttribute('data-section');
          if (sectionId) {
            setVisibleSections(prev => ({
              ...prev,
              [sectionId]: true,
            }));
          }
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Observe each section
    if (leadershipRef.current) observer.observe(leadershipRef.current);
    if (teamRef.current) observer.observe(teamRef.current);
    if (departmentsRef.current) observer.observe(departmentsRef.current);
    if (joinRef.current) observer.observe(joinRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // Function to render department icon based on iconType
  const renderDepartmentIcon = (iconType: string) => {
    switch (iconType) {
      case 'code':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
            />
          </svg>
        );
      case 'sliders':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
            />
          </svg>
        );
      case 'megaphone':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"
            />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-background">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="animate-hero-content mx-auto max-w-3xl text-center">
            <h1 className="hero-element mb-6 text-4xl font-bold">
              {intl.formatMessage({ id: 'public.team.title' })}
            </h1>
            <p className="hero-element hero-delay-1 text-xl opacity-90">
              {intl.formatMessage(
                { id: 'public.team.subtitle' },
                { company: displayName }
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Leadership team section */}
      <div
        ref={leadershipRef}
        data-section="leadership"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.leadership ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-5xl">
          <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold">
            {intl.formatMessage({ id: 'public.team.leadership' })}
          </h2>

          <div className="grid gap-12 md:grid-cols-2">
            {teamMembers.slice(0, 4).map((member, index) => (
              <div
                key={member.id}
                className="animate-fadeIn flex flex-col gap-6 md:flex-row"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="flex-shrink-0">
                  <div className="flex h-32 w-32 items-center justify-center overflow-hidden rounded-full bg-muted">
                    {/* In a real app, this would be an actual image */}
                    <span className="text-3xl text-muted-foreground">
                      {getText(member.name).charAt(0)}
                    </span>
                  </div>
                </div>
                <div>
                  <h3 className="mb-1 text-xl font-bold">
                    {getText(member.name)}
                  </h3>
                  <p className="mb-3 text-primary">
                    {getText(member.position)}
                  </p>
                  <p className="text-muted-foreground">{getText(member.bio)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Team members section */}
      <div
        ref={teamRef}
        data-section="team"
        className={`section-reveal bg-muted py-16 ${visibleSections.team ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-5xl">
            <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold">
              {intl.formatMessage({ id: 'public.team.our.team' })}
            </h2>

            <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
              {teamMembers.slice(2).map((member, index) => (
                <div
                  key={member.id}
                  className="animate-scaleIn text-center"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="mx-auto mb-4 flex h-32 w-32 items-center justify-center overflow-hidden rounded-full bg-muted">
                    {/* In a real app, this would be an actual image */}
                    <span className="text-3xl text-muted-foreground">
                      {getText(member.name).charAt(0)}
                    </span>
                  </div>
                  <h3 className="mb-1 text-lg font-medium">
                    {getText(member.name)}
                  </h3>
                  <p className="mb-3 text-primary">
                    {getText(member.position)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Departments section */}
      <div
        ref={departmentsRef}
        data-section="departments"
        className={`section-reveal container mx-auto px-4 py-16 ${visibleSections.departments ? 'visible' : ''}`}
      >
        <div className="mx-auto max-w-5xl">
          <h2 className="animate-slideUp mb-12 text-center text-3xl font-bold">
            {intl.formatMessage({ id: 'public.team.departments' })}
          </h2>

          <div className="grid gap-8 md:grid-cols-3">
            {departments.map((dept, index) => (
              <div
                key={index}
                className="stagger-card animate-fadeIn rounded-lg bg-muted p-6"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className="mb-4 text-primary">
                  {dept.iconType && renderDepartmentIcon(dept.iconType)}
                </div>
                <h3 className="mb-2 text-xl font-bold">{getText(dept.name)}</h3>
                <p className="mb-4 text-muted-foreground">
                  {getText(dept.description || '')}
                </p>
                <p className="text-sm text-muted-foreground">
                  {intl.formatMessage(
                    { id: 'public.team.member.count' },
                    { count: dept.memberCount }
                  )}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Join our team section */}
      <div
        ref={joinRef}
        data-section="join"
        className={`section-reveal bg-primary py-16 text-primary-foreground ${visibleSections.join ? 'visible' : ''}`}
      >
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="animate-fadeIn mb-6 text-3xl font-bold">
              {intl.formatMessage({ id: 'public.team.join.title' })}
            </h2>
            <p className="animate-fadeIn animation-delay-200 mb-8 text-lg opacity-90">
              {intl.formatMessage({ id: 'public.team.join.description' })}
            </p>
            <a
              href="/careers"
              className="shimmer-effect animate-fadeIn animation-delay-400 inline-block rounded-md bg-primary px-6 py-2 text-primary-foreground transition-colors hover:bg-primary/90"
            >
              {intl.formatMessage({ id: 'public.team.view.positions' })}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
