import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { updatePageMetadata } from '@/utils';
import { PAGE_METADATA } from '@/constants/site-config';
import { SupportTicketForm } from '@/components/forms/support-ticket-form';
import { SupportTicketFormType } from '@/types/public/support';

export default function Page() {
  const navigate = useNavigate();
  const intl = useIntl();
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    updatePageMetadata({
      ...PAGE_METADATA.supportTicket,
      title: intl.formatMessage({ id: 'public.support.ticket.create.title' }),
      description: intl.formatMessage({
        id: 'public.support.ticket.create.description',
      }),
    });
  }, [intl]);

  const handleSubmit = async (formData: SupportTicketFormType) => {
    setIsSubmitting(true);
    try {
      // In a real app, this would submit to the backend
      console.log('Support ticket submitted:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate to success page or show success message
      navigate('/support/tickets/success', {
        state: {
          ticketNumber: `TICKET-${Date.now()}`,
          email: formData.contactInfo.email,
        },
      });
    } catch (error) {
      console.error('Error submitting support ticket:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/support');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header section */}
      <div className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="mb-4 text-3xl font-bold">
              {intl.formatMessage({ id: 'public.support.ticket.create.title' })}
            </h1>
            <p className="text-xl opacity-90">
              {intl.formatMessage({
                id: 'public.support.ticket.create.subtitle',
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Form section */}
      <div className="container mx-auto px-4 py-12">
        <SupportTicketForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
      </div>
    </div>
  );
}
