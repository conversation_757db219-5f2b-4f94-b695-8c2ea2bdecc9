{"// Security Page": "", "security.title": "安全与合规", "security.subtitle": "您的数据安全是我们的首要任务。了解我们全面的安全措施和合规认证。", "security.certification.soc2": "SOC 2 认证", "security.certification.iso27001": "ISO 27001 认证", "security.certification.gdpr": "GDPR 合规", "security.overview.title": "企业级安全", "security.overview.description": "我们实施多层安全控制来保护您的数据，确保最高级别的可用性和性能。", "security.stat.uptime": "正常运行时间保证", "security.stat.monitoring": "安全监控", "security.stat.encryption": "AES 加密", "security.stat.backup": "数据备份", "security.stat.multiregion": "多区域", "security.measures.title": "安全措施", "security.measures.description": "全面的安全控制，旨在在各个层面保护您的数据。", "security.encryption.title": "端到端加密", "security.encryption.description": "所有数据在传输和静态时都使用行业标准的 AES-256 加密。", "security.encryption.feature1": "传输中数据使用 TLS 1.3", "security.encryption.feature2": "静态数据使用 AES-256", "security.encryption.feature3": "每 90 天轮换密钥", "security.monitoring.title": "24/7 安全监控", "security.monitoring.description": "持续监控和威胁检测，以识别和响应安全事件。", "security.monitoring.feature1": "实时威胁检测", "security.monitoring.feature2": "自动事件响应", "security.monitoring.feature3": "安全运营中心 (SOC)", "security.infrastructure.title": "安全基础设施", "security.infrastructure.description": "企业级基础设施，具有多层安全控制和冗余。", "security.infrastructure.feature1": "多区域数据中心", "security.infrastructure.feature2": "网络分段", "security.infrastructure.feature3": "DDoS 防护", "security.access.title": "访问控制", "security.access.description": "严格的访问控制和多因素身份验证，以保护敏感数据和系统。", "security.access.feature1": "多因素身份验证 (MFA)", "security.access.feature2": "基于角色的访问控制 (RBAC)", "security.access.feature3": "单点登录 (SSO) 集成", "security.backup.title": "数据备份与恢复", "security.backup.description": "自动备份和灾难恢复程序，确保业务连续性。", "security.backup.feature1": "每日自动备份", "security.backup.feature2": "时间点恢复", "security.backup.feature3": "99.9% 正常运行时间保证", "security.training.title": "安全培训", "security.training.description": "为所有团队成员提供定期安全培训和意识计划。", "security.training.feature1": "每月安全培训", "security.training.feature2": "钓鱼模拟测试", "security.training.feature3": "安全事件演练", "security.compliance.title": "合规认证", "security.compliance.description": "我们保持最高标准的行业法规和框架合规性。", "security.cert.soc2.name": "SOC 2 Type II", "security.cert.soc2.description": "安全性、可用性和机密性的审计控制", "security.cert.iso27001.name": "ISO 27001", "security.cert.iso27001.description": "信息安全管理国际标准", "security.cert.gdpr.name": "GDPR 合规", "security.cert.gdpr.description": "完全符合欧洲数据保护法规", "security.cert.hipaa.name": "HIPAA 合规", "security.cert.hipaa.description": "医疗数据保护和隐私合规", "security.cert.status.certified": "已认证", "security.cert.status.compliant": "合规", "security.cert.ongoing": "持续", "security.cert.valid.until": "有效期至", "security.policies.title": "安全政策与文档", "security.policies.description": "管理我们如何处理您的数据和维护安全的透明政策和程序。", "security.policy.privacy.title": "隐私政策", "security.policy.privacy.description": "我们如何收集、使用和保护您的个人信息", "security.policy.terms.title": "服务条款", "security.policy.terms.description": "使用我们服务的条款和条件", "security.policy.data.title": "数据处理协议", "security.policy.data.description": "我们如何处理和管理您的数据以符合法规", "security.policy.incident.title": "事件响应计划", "security.policy.incident.description": "我们处理安全事件和违规的程序", "security.policy.updated": "最后更新", "security.policy.read": "阅读政策", "security.contact.title": "报告安全问题", "security.contact.description": "如果您发现安全漏洞，请通过我们的负责任披露计划立即向我们报告。", "security.contact.email.title": "安全邮箱", "security.contact.email.description": "用于安全相关查询和漏洞报告", "security.contact.response.title": "响应时间", "security.contact.response.description": "我们在 24 小时内确认所有安全报告并提供定期更新", "security.contact.responsible.title": "负责任披露", "security.contact.responsible.description": "在我们有机会解决安全漏洞之前，请不要公开披露。我们致力于与安全研究人员合作，快速负责任地解决问题。"}